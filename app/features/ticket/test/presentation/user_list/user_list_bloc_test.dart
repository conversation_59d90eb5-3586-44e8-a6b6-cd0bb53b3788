/*
 * Created Date: Monday, 6th January 2025, 10:00:00
 * Author: <PERSON><PERSON><PERSON>
 * -----
 * Last Modified: Monday, 6th January 2025 10:00:00
 * Modified By: <PERSON><PERSON><PERSON>
 * -----
 * Copyright (c) 2021 - 2025 GAPO
 */

import 'package:flutter_test/flutter_test.dart';
import 'package:gp_core/models/assignee.dart';
import 'package:mockito/mockito.dart';

import '../../../lib/domain/domain.dart';
import '../../../lib/presentation/assignee_list/bloc/assignee_list_bloc.dart';

class MockTicketNodeAssigneesUseCase extends Mock
    implements TicketNodeAssigneesUseCase {}

void main() {
  group('UserListBloc', () {
    late AssigneeListBloc bloc;
    late MockTicketNodeAssigneesUseCase mockUseCase;

    setUp(() {
      mockUseCase = MockTicketNodeAssigneesUseCase();
      bloc = AssigneeListBloc();
    });

    tearDown(() {
      bloc.close();
    });

    test('should initialize with correct ticketId and nodeId', () {
      // Arrange
      const ticketId = 'ticket123';
      const nodeId = 'node456';

      // Act
      bloc.initialize(ticketId: ticketId, nodeId: nodeId);

      // Assert
      expect(bloc, isNotNull);
    });

    test('should update search query correctly', () {
      // Arrange
      const query = 'test user';

      // Act
      bloc.updateSearchQuery(query);

      // Assert
      expect(bloc, isNotNull);
    });

    test('should handle empty search query', () {
      // Arrange
      const query = '   ';

      // Act
      bloc.updateSearchQuery(query);

      // Assert
      expect(bloc, isNotNull);
    });
  });
}
