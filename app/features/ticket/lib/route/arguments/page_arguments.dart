import 'package:gp_core/models/assignee.dart';
import 'package:gp_feat_ticket/data/model/response/ticket/ticket_additional_request_response.dart';
import 'package:gp_feat_ticket/data/model/response/ticket/ticket_list_response.dart';
import 'package:gp_feat_ticket/domain/entity/ticket/ticket_list.entity.dart';
import 'package:gp_feat_ticket/domain/entity/ticket/ticket_node.entity.dart';
import 'package:gp_feat_ticket/presentation/create/ticket_create.page.dart';

/// Base class for all page arguments
abstract class PageArguments {}

/// Arguments for the ticket details page
class TicketDetailsArguments implements PageArguments {
  final String id;
  final TicketEntity? entity;

  TicketDetailsArguments({
    required this.id,
    this.entity,
  });
}

/// Arguments for the ticket create page
class TicketCreateArguments implements PageArguments {
  final TicketCreateMode mode;
  final dynamic ticketListResponse;

  TicketCreateArguments({
    this.mode = TicketCreateMode.create,
    this.ticketListResponse,
  });
}

/// Arguments for the ticket edit page
class TicketEditArguments implements PageArguments {
  final TicketListResponse ticketListResponse;
  final List<WorkflowFormFieldPermissionEntity>? permissions;
  final TicketNodeEntity nodeEntity;

  TicketEditArguments({
    required this.ticketListResponse,
    this.permissions,
    required this.nodeEntity,
  });
}

/// Arguments for the ticket additional request edit page
class TicketAdditionalRequestEditArguments implements PageArguments {
  final TicketListResponse ticketListResponse;
  final TicketAdditionalRequestResponse additionalRequestResponse;
  final List<WorkflowFormFieldPermissionEntity>? fieldPermissions;
  final TicketNodeEntity ticketNodeEntity;

  TicketAdditionalRequestEditArguments({
    required this.ticketListResponse,
    required this.additionalRequestResponse,
    this.fieldPermissions,
    required this.ticketNodeEntity,
  });
}

/// Arguments for the user list page
class AssigneeListArguments implements PageArguments {
  final String ticketId;
  final String nodeId;
  final String? title;
  final Function(Assignee)? onUserSelected;
  final bool? ignoreMe;

  AssigneeListArguments({
    required this.ticketId,
    required this.nodeId,
    this.title,
    this.onUserSelected,
    this.ignoreMe,
  });
}
