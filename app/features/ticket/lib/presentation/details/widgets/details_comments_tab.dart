import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_core/core.dart';
import 'package:gp_core/models/comment/comment.dart';
import 'package:gp_core/shared_features/comment/input/controller.dart';
import 'package:gp_core/shared_features/comment/medias/media.dart';
import 'package:gp_core/shared_features/comment/reply_comment/controller/reply_comment_controller.dart';
import 'package:gp_feat_ticket/constants/url/ticket/ticket.dart';
import 'package:gp_feat_ticket/data/data.dart';
import 'package:gp_feat_ticket/domain/entity/entity.dart';
import 'package:gp_feat_ticket/domain/usecase/ticket/ticket_get_comment.usecase.dart';
import 'package:gp_feat_ticket/presentation/details/comments/bloc/bloc.dart';
import 'package:gp_feat_ticket/presentation/details/mixins/node_graph.dart';
import 'package:gp_feat_ticket/presentation/details/mixins/comment_handler_mixin.dart';
import 'package:gp_feat_ticket/presentation/details/widgets/comment/comment_input.dart';
import 'package:gp_feat_ticket/presentation/details/widgets/comment/comment_wrapper.dart';

class TicketCommentTabWidget extends StatefulWidget {
  const TicketCommentTabWidget({
    super.key,
    required this.ticketEntity,
    this.nodeEntity,
  });

  final TicketNodeEntity? nodeEntity;
  final TicketEntity ticketEntity;
  @override
  State<TicketCommentTabWidget> createState() => _TicketCommentTabWidgetState();
}

class _TicketCommentTabWidgetState extends State<TicketCommentTabWidget>
    with NodeGraphMixin, CommentHandlerMixin {
  List<Assignee>? membersCanMention;
  String currentNodeId = '';
  String currentTicketId = '';
  final TicketDetailsCommentsBloc commentsBloc = TicketDetailsCommentsBloc();
  final RefreshController refreshController = RefreshController();
  final AutoScrollController scrollController =
      AutoScrollController(axis: Axis.vertical);

  late InputCommentController inputCommentController;

  List<TicketCommentEntity> comments = [];

  @override
  void initState() {
    super.initState();
    inputCommentController = InputCommentController(
      onCommentCallback: OnCommentCallback(
        onNewCommentLocal: _onNewCommentLocal,
        onMediasCallback: OnMediasCallback(
          onCommentHasMedias: _onCommentHasMedias,
          onCommentUploadMediasError: _onCommentUploadMediasError,
        ),
        onEditComment: _onEditComment,
        onCreateComment: _onCommentCreated,
        onFinally: dismissKeyboard,
      ),
      onCommentItemCallback: OnCommentItemCallback(
        afterClickDeleteComment: _afterClickDeleteComment,
        getScrollOffset: () => scrollController.position.pixels,
      ),
    );

    if (widget.nodeEntity != null) {
      currentNodeId = widget.nodeEntity!.id.toString();
      currentTicketId = widget.nodeEntity!.ticketId.toString();
      commentsBloc.add(TicketDetailsGetCommentsEvent(
        ticketId: currentTicketId,
        nodeId: currentNodeId,
        params: TicketCommentsParams(),
      ));
    }
  }

  void onLoadMoreComment() {
    commentsBloc.add(
      TicketDetailsGetCommentsEvent(
        ticketId: currentTicketId,
        nodeId: currentNodeId,
        isLoadMore: true,
        params: TicketCommentsParams(),
      ),
    );
  }

  void reloadData() {
    refreshController.footerMode?.value = LoadStatus.canLoading;
    commentsBloc.add(
      TicketDetailsGetCommentsEvent(
        isReload: true,
        ticketId: currentTicketId,
        nodeId: currentNodeId,
        params: TicketCommentsParams(),
      ),
    );
  }

  TicketCommentEntity _onNewCommentLocal() {
    return newCommentLocal(
      inputCommentController.commentStr.value,
      commentMediaLocals: inputCommentController
          .commentMediasController?.commentMedias
          .toList(),
      idParent: inputCommentController.idParent,
    );
  }

  TicketCommentEntity newCommentLocal(
    String value, {
    List<CommentMediaModel>? commentMediaLocals,
    String? idParent,
  }) {
    return TicketCommentEntity(
      ticketNodeId: int.parse(currentNodeId),
      targetId: currentTicketId,
      targetType: 'ticket',
      text: value,
      mentions: inputCommentController.textEditingController.mentions.toList(),
      user: User(
        avatar: Constants.avatar() ?? "",
        avatarThumbPattern: Constants.avatar() ?? "",
        displayName: Constants.displayName(),
        fullName: Constants.displayName(),
        userId: Constants.userId(),
      ),
      commentAs: CommentAs(authorId: Constants.userId(), authorType: 'user'),
      dataSource: 3,
      parentId: idParent,
    )
      ..commentMediaLocals = commentMediaLocals
      ..createdAt
      ..updatedAt = DateTime.now().microsecondsSinceEpoch;
  }

  void _onCommentHasMedias(
    Comment comment,
    bool isResendComment,
    bool isEditingComment,
  ) {
    commentsBloc.add(TicketDetailsMediaCommentEvent(
      comment: comment as TicketCommentEntity,
      isResendComment: isResendComment,
      isEditingComment: isEditingComment,
    ));
  }

  void afterCreateNewLocalComment(Comment comment) {
    comment.mentions =
        inputCommentController.textEditingController.mentions.toList();

    inputCommentController.clearText();
    inputCommentController.clearMedias();
    dismissKeyboard();
  }

  void handleMediaComment(
    TicketCommentEntity comment,
    bool isResendComment,
    bool isEditingComment,
  ) {
    afterCreateNewLocalComment(comment);

    if (comment.parentId == null) {
      if (isEditingComment == false) {
        comments.add(comment);
      }

      if (isResendComment) {
        comment.commentMediaLocals!.first.local?.error = null;
      }
    }
  }

  void _onCommentUploadMediasError() {
    comments.removeLast();
  }

  Future _onEditComment(String? id, Comment comment) async {
    Comment eComment = comment;

    eComment.id = id;

    editComment(eComment);

    comments.remove(comment);
  }

  Future scrollToPositon(double px) async {
    await 200.milliseconds.delay();
    await scrollController.animateTo(px,
        curve: Curves.easeOut, duration: const Duration(milliseconds: 200));
  }

  void editComment(Comment comment) {
    commentsBloc.add(TicketDetailsEditCommentsEvent(
      comment: comment,
      commentId: comment.id ?? '',
      hasMedias: inputCommentController.hasMedias.value,
    ));
  }

  void afterEditComment() {
    inputCommentController.clearText();
    inputCommentController.createCommentMode = CreateCommentMode.create;
    inputCommentController.clearMedias();

    dismissKeyboard();

    scrollToPositon(inputCommentController.scrollCurrentPositionPx);
  }

  Future _onCommentCreated(bool hasMedias, Comment comment) async {
    commentsBloc.add(TicketDetailsPostCommentsEvent(
      ticketId: currentTicketId,
      nodeId: currentNodeId,
      comment: comment as TicketCommentEntity,
      hasMedias: hasMedias,
    ));
  }

  void dismissKeyboard() {
    FocusManager.instance.primaryFocus?.unfocus();
  }

  Future _afterClickDeleteComment(Comment comment) async {
    commentsBloc.add(TicketDetailsDeleteCommentsEvent(
      comment: comment as TicketCommentEntity,
    ));
  }

  Future<ReplyListCommentResponse> getListComment(String commentId) async {
    return await commentsBloc.replyGetListComment(TicketCommentInput(
      ticketId: currentTicketId,
      nodeId: currentNodeId,
      params: TicketCommentsParams(parentId: commentId),
    ));
  }

  Future<ReplyListCommentResponse> getNextComment(String nextLink) async {
    // nextLink example: after=1234567890&parent_id=1234567890...
    final uri = Uri.parse(
        "${Constants.baseUrl}${TicketUrlConstants.kTicketDomain}?$nextLink");
    return await commentsBloc.replyGetListComment(TicketCommentInput(
      ticketId: currentTicketId,
      nodeId: currentNodeId,
      params: TicketCommentsParams(
          after: uri.queryParameters['after'],
          parentId: uri.queryParameters['parent_id']),
    ));
  }

  void handleDataLoaded(TicketDetailsCommentsLoadedState state) {
    if (state.isReload == false && state.isLoadMore == false) {
      commentsBloc.add(TicketDetailsGetMembersEvent(
        ticketEntity: widget.ticketEntity,
        ticketNodeEntity: widget.nodeEntity!,
      ));
    }
    if (state.isLoadMore) {
      comments.addAll(state.comments);
    } else {
      comments = state.comments;
      refreshController.refreshCompleted();
    }
    if (!state.canLoadMore) {
      refreshController.loadNoData();
      return;
    }
    refreshController.loadComplete();
  }

  void handleTicketGetMembersSuccess(
    BuildContext context,
    TicketDetailsGetMembersSuccessState state,
  ) {
    membersCanMention = state.members;
    inputCommentController.setMembersCanMention(membersCanMention ?? []);
  }

  @override
  void onReloadData() {
    reloadData();
  }

  Widget _buildCommentList() {
    return SmartRefresher(
      controller: refreshController,
      scrollController: scrollController,
      enablePullUp: true,
      onLoading: onLoadMoreComment,
      onRefresh: reloadData,
      header: const GPPullToRefreshHeader(),
      footer: const GPPullToRefreshFooter(),
      child: _listComments(),
    );
  }

  Widget _buildEmptyState() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        const SizedBox(height: 64),
        const SvgWidget(
            Assets.PACKAGES_GP_ASSETS_IMAGES_SVG_IMG_EMPTY_DATA_SVG),
        const SizedBox(height: 16),
        Text(
          LocaleKeys.ticket_details_comment_tabs_empty.tr,
          style: textStyle(GPTypography.bodyLarge)
              ?.copyWith(color: GPColor.contentSecondary),
        ),
        const Spacer(),
        CommentInputRow(controller: inputCommentController),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(top: 8),
      child: Scaffold(
        backgroundColor: GPColor.bgPrimary,
        body: SafeArea(
          child: BlocProvider<TicketDetailsCommentsBloc>(
            create: (context) => commentsBloc,
            child: BlocConsumer<TicketDetailsCommentsBloc,
                TicketDetailsCommentsState>(
              listener: (context, state) {
                if (state is TicketDetailsCommentsLoadedState) {
                  handleDataLoaded(state);
                }
                if (state is TicketDetailsNewCommentsAddedState) {
                  handleNewComment(
                    comment: state.comment,
                    newComment: state.responseComment,
                    hasMedias: state.hasMedias,
                    comments: comments,
                  );
                }
                if (state is TicketDetailsEditCommentsState) {
                  handleEditComment(
                    responseComment: state.responseComment,
                    comments: comments,
                    afterEditComment: afterEditComment,
                  );
                }
                if (state is TicketDetailsDeleteCommentsState) {
                  handleDeleteComment(state.comment, comments);
                }
                if (state is TicketDetailsGetMembersSuccessState) {
                  handleTicketGetMembersSuccess(context, state);
                }
                if (state is TicketDetailsMediaCommentState) {
                  handleMediaComment(
                    state.comment,
                    state.isResendComment,
                    state.isEditingComment,
                  );
                }
              },
              builder: (context, state) {
                if (state is TicketDetailsCommentsDataState) {
                  return comments.isEmpty
                      ? _buildEmptyState()
                      : Column(
                          children: [
                            Expanded(child: _buildCommentList()),
                            CommentInputRow(controller: inputCommentController),
                          ],
                        );
                }
                return const Center(
                  child: CircularProgressIndicator(),
                );
              },
            ),
          ),
        ),
      ),
    );
  }

  Widget _listComments() {
    List<Widget> listComment = [];
    for (var i = 0; i < comments.length; i++) {
      listComment.add(CommentWrapper(
        scrollController: scrollController,
        inputCommentController: inputCommentController,
        comments: comments,
        index: i,
        onReplyCommentCallback: OnReplyCommentCallback(
          onGetListComment: (commentId) async =>
              await getListComment(commentId),
          onGetNextComment: (nextLink) => getNextComment(nextLink),
        ),
      ));
    }
    return listComment.isNotEmpty
        ? Padding(
            padding: const EdgeInsets.only(top: 16),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: listComment,
            ),
          )
        : const SizedBox();
  }
}
