// ignore_for_file: public_member_api_docs

import 'dart:async';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_core/configs/constants.dart';
import 'package:gp_core/models/assignee.dart';
import 'package:gp_core_v2/gp_core_v2.dart';
import 'package:gp_feat_ticket/data/model/request/ticket/list/assignee/ticket_list_assignee_params.dart';
import 'package:gp_feat_ticket/mapper/ticket_mapper.dart';
import 'package:gp_feat_ticket/presentation/assignee_list/bloc/assignee_list_state.dart';
import 'package:gp_feat_ticket/presentation/assignee_list/model/assignee_list_entity.dart';
import 'package:gp_shared/lib.dart';
import 'package:injectable/injectable.dart';
import 'package:get_it/get_it.dart';

import '../../../domain/domain.dart';

@Injectable()
class AssigneeListBloc extends GPBaseListBlocV2<AssigneeListEntity,
        ListAPIResponseV2<Assignee>, TicketListAssigneeParams>
    with
        _VariableMixin,
        GPTicketMapperMixin
    implements
        BaseListBehavior<AssigneeListEntity, ListAPIResponseV2<Assignee>,
            TicketListAssigneeParams> {
  AssigneeListBloc() {
    setOnUseCaseBehavior(this);
  }

  String? _ticketId;
  String? _nodeId;
  String? _searchQuery;
  bool _ignoreMe = false;

  void initialize({
    required String ticketId,
    required String nodeId,
    bool ignoreMe = false,
  }) {
    _ticketId = ticketId;
    _nodeId = nodeId;
    _ignoreMe = ignoreMe;
  }

  void updateSearchQuery(String query) {
    _searchQuery = query.trim().isEmpty ? null : query.trim();
  }

  @override
  Future<ListAPIResponseV2<Assignee>?> usecaseOnLoadData({
    bool isRefreshData = false,
    dynamic inputData,
    String? nextLink,
    int? page,
  }) async {
    if (_ticketId == null || _nodeId == null) {
      throw Exception('TicketId and NodeId must be initialized');
    }

    final input = TicketNodeAssigneesInput(
      ticketId: _ticketId!,
      nodeId: _nodeId!,
      search: _searchQuery,
      limit: 10,
      nextLink: nextLink,
    );

    return await ticketNodeAssigneesUseCase.execute(input);
  }

  @override
  Future<ListAPIResponseV2<Assignee>?> usecaseOnSearchData({
    required dynamic params,
    bool isRefreshData = false,
    dynamic inputData,
    String? nextLink,
    int? page,
  }) async {
    return usecaseOnLoadData(
      isRefreshData: isRefreshData,
      inputData: inputData,
      nextLink: nextLink,
      page: page,
    );
  }

  @override
  Future<TicketListAssigneeLoadedState> emitData({
    ListAPIResponseV2<Assignee>? response,
    List<AssigneeListEntity>? entityData,
    required Emitter<BaseListState> emit,
    required bool isInitialLoad,
  }) async {
    bool canNextPage = false;
    if (response?.links?.next?.isNotEmpty == true) {
      canNextPage = true;
    }
    final assignees = response?.data ?? [];
    if (_ignoreMe) {
      assignees.removeWhere((element) => element.id == Constants.userId());
    }
    final data = assignees.map((e) => AssigneeListEntity(assignee: e)).toList();

    final state = TicketListAssigneeLoadedState(
      isInitialLoad: isInitialLoad,
      data: data,
      canNextPage: canNextPage,
      nextLink: nextLink(response?.links),
    );

    return state;
  }
}

mixin _VariableMixin {
  final TicketNodeAssigneesUseCase ticketNodeAssigneesUseCase =
      GetIt.I<TicketNodeAssigneesUseCase>();
}
