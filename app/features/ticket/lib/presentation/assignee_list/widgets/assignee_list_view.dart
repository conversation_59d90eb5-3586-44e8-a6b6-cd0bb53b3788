// ignore_for_file: public_member_api_docs

import 'package:flutter/material.dart';
import 'package:gp_core/models/assignee.dart';
import 'package:gp_core_v2/base/widgets/base_list_widget.dart';
import 'package:gp_shared/presentation/base/base.dart';

import '../model/assignee_list_entity.dart';
import 'assignee_list_item.dart';

class AssigneeListView extends GPBaseListViewV2<AssigneeListEntity> {
  AssigneeListView({
    required super.params,
    required this.onUserTap,
    super.key,
  });

  final Function(Assignee) onUserTap;

  @override
  Widget buildItem(
    ListViewMode mode,
    BuildContext context,
    AssigneeListEntity item,
    int index,
  ) {
    return AssigneeListItem(
      user: item.assignee,
      onTap: () => onUserTap(item.assignee),
    );
  }

  @override
  Widget? buildFirstPageProgressIndicator(
    ListViewMode mode,
    BuildContext context,
  ) {
    return const Center(
      child: CircularProgressIndicator(),
    );
  }
}
