// ignore_for_file: public_member_api_docs

import 'package:flutter/material.dart';
import 'package:gp_core/core.dart';

class AssigneeListItem extends StatelessWidget {
  const AssigneeListItem({
    required this.user,
    required this.onTap,
    super.key,
  });

  final Assignee user;
  final VoidCallback onTap;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        child: Row(
          children: [
            // Avatar
            AssigneeAvatar(
              avatarThumbPattern: (user.avatarThumbPattern?.isEmpty ?? false)
                  ? user.avatar
                  : user.avatarThumbPattern,
              size: 40,
              displayName: user.displayName,
            ),
            const SizedBox(width: 12),
            // User info
            Expanded(
                child: AssigneeContentWidget(
              displayName: user.displayName,
              department: user.userDepartment,
              role: user.userRole,
            )),
          ],
        ),
      ),
    );
  }
}

class AssigneeContentWidget extends StatelessWidget {
  final String displayName;
  final String? department;
  final String? role;

  const AssigneeContentWidget({
    super.key,
    required this.displayName,
    this.department,
    this.role,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          displayName,
          style: textStyle(GPTypography.headingSmall)
              ?.mergeFontWeight(FontWeight.w600),
        ),
        if (department != null && department!.isNotEmpty)
          Row(
            children: [
              SvgWidget(
                "assets/images/ic24-fill-organizational-chart.png",
                color: GPColor.contentSecondary,
                width: 15,
                height: 15,
              ),
              const SizedBox(width: 8),
              Flexible(
                child: Text(
                  department ?? '',
                  overflow: TextOverflow.ellipsis,
                  style: textStyle(GPTypography.bodyMedium)
                      ?.mergeColor(GPColor.contentSecondary),
                ),
              ),
            ],
          ),
        if (role != null && role!.isNotEmpty)
          Row(
            children: [
              SvgWidget(
                "assets/images/ic24-fill-briefcase.png",
                color: GPColor.contentSecondary,
                width: 15,
                height: 15,
              ),
              const SizedBox(width: 8),
              Flexible(
                child: Text(
                  role ?? '',
                  overflow: TextOverflow.ellipsis,
                  style: textStyle(GPTypography.bodyMedium)
                      ?.mergeColor(GPColor.contentSecondary),
                ),
              ),
            ],
          ),
        const SizedBox(height: 4),
      ],
    );
  }
}
