// ignore_for_file: public_member_api_docs

import 'dart:async';

import 'package:flutter/material.dart';
import 'package:gp_core/core.dart';
import 'package:gp_shared/presentation/base/base.dart';

import 'bloc/assignee_list_bloc.dart';
import 'widgets/assignee_list_view.dart';

class AssigneeListScreen extends StatefulWidget {
  const AssigneeListScreen({
    required this.ticketId,
    required this.nodeId,
    this.title,
    this.onUserSelected,
    this.ignoreMe,
    super.key,
  });

  final String ticketId;
  final String nodeId;
  final String? title;
  final Function(Assignee)? onUserSelected;
  final bool? ignoreMe;

  @override
  State<AssigneeListScreen> createState() => _AssigneeListScreenState();
}

class _AssigneeListScreenState extends State<AssigneeListScreen> {
  AssigneeListBloc bloc = AssigneeListBloc();
  final TextEditingController searchController = TextEditingController();
  Timer? _debounce;

  late GPBaseListViewParamsV2 listParams;

  @override
  void initState() {
    super.initState();
    bloc.initialize(
      ticketId: widget.ticketId,
      nodeId: widget.nodeId,
    );

    listParams = GPBaseListViewParamsV2(
      listBloc: bloc,
      needLoadDataWhenInitial: true,
      onRefresh: () {
        bloc.loadData(isInitialLoad: true);
      },
    );

    searchController.addListener(_onSearchChanged);
  }

  @override
  void dispose() {
    _debounce?.cancel();
    searchController.dispose();
    super.dispose();
  }

  void _onSearchChanged() {
    if (_debounce?.isActive ?? false) _debounce!.cancel();
    _debounce = Timer(const Duration(milliseconds: 500), () {
      bloc.updateSearchQuery(searchController.text);
      bloc.loadData(isInitialLoad: true);
    });
  }

  void _onUserTap(Assignee user) {
    widget.onUserSelected?.call(user);
    Utils.back();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.title ?? '',
            style: textStyle(GPTypography.headingMedium)),
        leading: GPBackButton(action: () => Utils.back()),
        elevation: 1,
      ),
      body: NestedScrollView(
        floatHeaderSlivers: true,
        headerSliverBuilder: (context, innerBoxIsScrolled) {
          return [
            SliverAppBar(
              elevation: 0,
              leading: const SizedBox(),
              floating: true,
              flexibleSpace: GPSearchBar(
                onTap: () {},
                height: 44,
                borderRadius: BorderRadius.circular(8),
                textEditingController: searchController,
                hintText: LocaleKeys.memberPicker_assignee_search_text.tr,
                showClearBtn: true,
                showBorder: true,
              ).paddingSymmetric(horizontal: 12).paddingOnly(top: 10),
            ),
          ];
        },
        body: GPBaseListViewWrapperV2(
          params: listParams,
          builder: (GPBaseListViewParamsV2 params) {
            return AssigneeListView(
              params: params,
              onUserTap: _onUserTap,
            );
          },
        ),
      ),
    );
  }
}
