/*
 * Created Date: Wednesday, 3rd July 2024, 09:00:47
 * Author: gapo
 * -----
 * Last Modified: Friday, 11th April 2025 16:38:37
 * Modified By: <PERSON><PERSON><PERSON>
 * -----
 * Copyright (c) 2021 - 2025 GAPO
 */

// ignore_for_file: public_member_api_docs

import '../workflow/workflow_url.constants.dart';

/// GapoTicketUrl
final class TicketUrlConstants {
  /// chung workflow domain
  static const String kTicketDomain = WorkFlowUrlConstants.kWorkFlowDomain;
  static const String kTicketUrl = '/tickets';

  /// staging là /tickets/v3
  /// product là /tickets
  static const String kTicketV3Url = '/tickets/v3';
  static const String kTicketEsUrl = '/tickets/es';

  /// staging là /full-flowcharts
  /// product là /flowcharts
  static const String kFlowChartUrl = '/full-flowcharts';
  static const String kFlowChartEsUrl = '/full-flowcharts/es';
  static const String kNodeUrl = '/nodes';
  static const String kAssigneeUrl = '/assignee';
  static const String kFollowerUrl = '/followers';

  /// Yêu cầu bổ sung thông tin
  static const String kAdditionalRequestsUrl = '/additional-requests';

  /// Activities
  static const String kActivityRequestsUrl = '/activities';

  /// phản hồi yêu cầu bổ sung thông tin
  static const String kResponseAdditionalRequestsUrl = '/responses';

  /// cập nhật lại fields trong ticket
  static const String kFieldsUrl = '/fields';

  /// status
  static const String kStatusUrl = '/status';

  /// chuyển về spam
  static const String kSpamReportsUrl = '/spam-reports';

  /// bảo lưu yêu cầu
  static const String kOnHoldUrl = '/on-hold-requests';

  /// phê duyệt bảo lưu yêu cầu
  static const String kAcceptOnHoldUrl = '/accept';

  /// từ chối bảo lưu yêu cầu
  static const String kRejectOnHoldUrl = '/reject';

  /// huỷ bảo lưu yêu cầu
  static const String kCancelOnHoldUrl = '/cancel';

  /// lấy thành viên
  static const String kMembers = '/members';

  /// lấy danh sách user follow
  static const String kFollowMembers = '/follow_members';

  /// lấy ds bình luận
  static const String kCommentsUrl = '/comments';

  /// bỏ theo dõi
  static const String kUnfollowUrl = '/unfollow';

  /// mở lại ticket
  static const String kReopenUrl = '/reopen';

  /// đóng ticket
  static const String kCloseUrl = '/close';

  /// đánh giá ticket
  static const String kReviewUrl = '/review';

  /// huỷ ticket
  static const String kCancelUrl = '/cancel';

  /// gán nhãn dán
  static const String kLabelUrl = '/tags';

  /// check có phải assignee của ticket
  static const String kIsAssigneeUrl = '/participants/is-assignee';

  /// get nodes that can be redone
  static const String kCanRedoNodesUrl = '/can-redo-nodes';

  /// get end node that can be redone
  static const String kCanRedoEndNodeUrl = '/can-redo-end-nodes';

  /// redo end nodes
  static const String kRedoEndNodesUrl = '/redo-end-nodes';

  /// redo previous steps
  static const String kRedoPreviousUrl = '/redo-previous';

  /// list assignee
  static const String kListAssigneeUrl = '/assignees';
}
