// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'ticket_list_assignee_params.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$TicketListAssigneeParams {
  @JsonKey(name: 'name')
  String? get name; //
  int get limit;
  String? get next;

  /// Create a copy of TicketListAssigneeParams
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $TicketListAssigneeParamsCopyWith<TicketListAssigneeParams> get copyWith =>
      _$TicketListAssigneeParamsCopyWithImpl<TicketListAssigneeParams>(
          this as TicketListAssigneeParams, _$identity);

  /// Serializes this TicketListAssigneeParams to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is TicketListAssigneeParams &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.limit, limit) || other.limit == limit) &&
            (identical(other.next, next) || other.next == next));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, name, limit, next);

  @override
  String toString() {
    return 'TicketListAssigneeParams(name: $name, limit: $limit, next: $next)';
  }
}

/// @nodoc
abstract mixin class $TicketListAssigneeParamsCopyWith<$Res> {
  factory $TicketListAssigneeParamsCopyWith(TicketListAssigneeParams value,
          $Res Function(TicketListAssigneeParams) _then) =
      _$TicketListAssigneeParamsCopyWithImpl;
  @useResult
  $Res call({@JsonKey(name: 'name') String? name, int limit, String? next});
}

/// @nodoc
class _$TicketListAssigneeParamsCopyWithImpl<$Res>
    implements $TicketListAssigneeParamsCopyWith<$Res> {
  _$TicketListAssigneeParamsCopyWithImpl(this._self, this._then);

  final TicketListAssigneeParams _self;
  final $Res Function(TicketListAssigneeParams) _then;

  /// Create a copy of TicketListAssigneeParams
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? name = freezed,
    Object? limit = null,
    Object? next = freezed,
  }) {
    return _then(_self.copyWith(
      name: freezed == name
          ? _self.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      limit: null == limit
          ? _self.limit
          : limit // ignore: cast_nullable_to_non_nullable
              as int,
      next: freezed == next
          ? _self.next
          : next // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable(createFactory: false)
class _TicketListAssigneeParams implements TicketListAssigneeParams {
  _TicketListAssigneeParams(
      {@JsonKey(name: 'name') this.name, this.limit = 30, this.next});

  @override
  @JsonKey(name: 'name')
  final String? name;
//
  @override
  @JsonKey()
  final int limit;
  @override
  final String? next;

  /// Create a copy of TicketListAssigneeParams
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$TicketListAssigneeParamsCopyWith<_TicketListAssigneeParams> get copyWith =>
      __$TicketListAssigneeParamsCopyWithImpl<_TicketListAssigneeParams>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$TicketListAssigneeParamsToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _TicketListAssigneeParams &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.limit, limit) || other.limit == limit) &&
            (identical(other.next, next) || other.next == next));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, name, limit, next);

  @override
  String toString() {
    return 'TicketListAssigneeParams(name: $name, limit: $limit, next: $next)';
  }
}

/// @nodoc
abstract mixin class _$TicketListAssigneeParamsCopyWith<$Res>
    implements $TicketListAssigneeParamsCopyWith<$Res> {
  factory _$TicketListAssigneeParamsCopyWith(_TicketListAssigneeParams value,
          $Res Function(_TicketListAssigneeParams) _then) =
      __$TicketListAssigneeParamsCopyWithImpl;
  @override
  @useResult
  $Res call({@JsonKey(name: 'name') String? name, int limit, String? next});
}

/// @nodoc
class __$TicketListAssigneeParamsCopyWithImpl<$Res>
    implements _$TicketListAssigneeParamsCopyWith<$Res> {
  __$TicketListAssigneeParamsCopyWithImpl(this._self, this._then);

  final _TicketListAssigneeParams _self;
  final $Res Function(_TicketListAssigneeParams) _then;

  /// Create a copy of TicketListAssigneeParams
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? name = freezed,
    Object? limit = null,
    Object? next = freezed,
  }) {
    return _then(_TicketListAssigneeParams(
      name: freezed == name
          ? _self.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      limit: null == limit
          ? _self.limit
          : limit // ignore: cast_nullable_to_non_nullable
              as int,
      next: freezed == next
          ? _self.next
          : next // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

// dart format on
