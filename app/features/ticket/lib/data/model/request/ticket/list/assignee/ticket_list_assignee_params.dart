// ignore_for_file: invalid_annotation_target, use_function_type_syntax_for_parameters

import 'package:freezed_annotation/freezed_annotation.dart';

part 'ticket_list_assignee_params.freezed.dart';
part 'ticket_list_assignee_params.g.dart';

@Freezed(
  toJson: true,
  fromJson: false,
)
abstract class TicketListAssigneeParams with _$TicketListAssigneeParams {
  factory TicketListAssigneeParams({
    @JsonKey(name: 'name') String? name,
    //
    @Default(30) int limit,

    String? next,
  }) = _TicketListAssigneeParams;
}
