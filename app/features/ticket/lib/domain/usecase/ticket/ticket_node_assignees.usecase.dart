// ignore_for_file: public_member_api_docs

import 'package:gp_core/models/assignee.dart';
import 'package:gp_core_v2/gp_core_v2.dart';
import 'package:gp_feat_ticket/data/model/request/ticket/list/assignee/ticket_list_assignee_params.dart';
import 'package:injectable/injectable.dart';

import '../../domain.dart';

@Injectable(order: DiConstants.kDomainUseCaseOrder)
class TicketNodeAssigneesUseCase extends GPBaseFutureUseCase<
    TicketNodeAssigneesInput, ListAPIResponseV2<Assignee>> {
  TicketNodeAssigneesUseCase(
    @Named('kTicketRepository') this._ticketRepository,
  );

  final TicketRepository _ticketRepository;

  @override
  Future<ListAPIResponseV2<Assignee>> buildUseCase(
    TicketNodeAssigneesInput input,
  ) async {
    final params = TicketListAssigneeParams(
      limit: input.limit,
      name: input.search,
      next: input.nextLink,
    );
    final response = await _ticketRepository.getNodeAssignees(
      ticketId: input.ticketId,
      nodeId: input.nodeId,
      params: params,
    );

    return response;
  }
}

class TicketNodeAssigneesInput extends GPBaseInput {
  const TicketNodeAssigneesInput({
    required this.ticketId,
    required this.nodeId,
    this.search,
    this.limit = 30,
    this.ignoreMe = false,
    this.nextLink,
  });

  final String ticketId;
  final String nodeId;
  final String? search;
  final int limit;
  final bool ignoreMe;
  final String? nextLink;
}
