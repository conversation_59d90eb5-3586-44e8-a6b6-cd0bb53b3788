import 'package:diffutil_dart/diffutil.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:gp_core/core.dart';
import 'package:gp_feat_ticket/domain/entity/workflow/workflow_tag.entity.dart';
import 'package:gp_feat_ticket/lib.dart';
import 'package:gp_feat_ticket/presentation/details/widgets/popup/add_follower/add_follower_bottom_sheet.dart';
import 'package:gp_feat_ticket/domain/entity/enums/ticket/action/add_follower_option.dart';
import 'package:gp_shared/domain/entity/entity.dart';
import 'package:gp_shared/widgets/member_picker/member_picker_result.dart';
import 'package:gp_shared/widgets/member_picker/member_picker_wrapper.dart';

import 'follower_utils.dart';

const _kUnUsedPriority = 99;
final _mapper = GetIt.I<GPTicketMapper>(instanceName: 'kGPTicketMapper');

enum TicketActionResultType {
  reloadData,
  doNothing,
}

enum TicketAction {
  /// xêm thêm
  more(
    asset: Assets.PACKAGES_GP_ASSETS_IMAGES_SVG_IC20_FILL_3DOT_HORIZONTAL_SVG,
    priority: _kUnUsedPriority,
  ),

  /// có thể comment, không hiển thị ở bottomSheet,
  /// priority cần khác `_kUnUsedPriority` để có thể hiển thị tab comment
  comment(
    priority: 98,
  ),

  /// bổ sung thông tin (chỉ khi người thực hiện yêu cầu bổ sung thông tin)
  provideInfo(
    priority: _kUnUsedPriority,
  ),

  /// Chỉnh sửa thông tin đã bổ sung
  edit(
    priority: _kUnUsedPriority,
  ),
  duplicate(
    asset: Assets.PACKAGES_GP_ASSETS_IMAGES_SVG_IC20_LINE15_2SQUARE_SVG,
    priority: 63,
  ),
  editField(
    asset: Assets.PACKAGES_GP_ASSETS_IMAGES_SVG_IC20_LINE15_PENCIL_SVG,
    priority: 34,
  ),
  closeTicket(
    asset: Assets.PACKAGES_GP_ASSETS_IMAGES_SVG_IC20_FILL_CHECKMARK_CIRCLE_SVG,
    priority: 2,
  ),
  reopenTicket(
    asset: Assets.PACKAGES_GP_ASSETS_IMAGES_SVG_IC20_LINE15_DOOR_SVG,
    priority: 23,
  ),
  spam(
    asset: Assets.PACKAGES_GP_ASSETS_IMAGES_SVG_IC24_LINE15_WARNINGMARK_PNG,
    priority: 58,
  ),
  chat(
    asset: Assets
        .PACKAGES_GP_ASSETS_IMAGES_SVG_IC24_LINE15_BUBBLE_ELLIPSE_3DOT_CHECKMARK_SVG,
    priority: 65,
  ),
  cancelTicket(
    asset: Assets.PACKAGES_GP_ASSETS_IMAGES_SVG_IC20_LINE15_XMARK_SVG,
    priority: 74,
  ),
  deleteTicket(
    asset: Assets.PACKAGES_GP_ASSETS_IMAGES_SVG_IC20_LINE15_TRASH_SVG,
    priority: 77,
  ),
  approve(
    asset: Assets.PACKAGES_GP_ASSETS_IMAGES_SVG_IC20_FILL_CHECKMARK_CIRCLE_SVG,
    priority: 5,
  ),
  reject(
    asset: Assets.PACKAGES_GP_ASSETS_IMAGES_SVG_IC20_FILL_XMARK_CIRCLE_SVG,
    priority: 6,
  ),

  /// chuyển bước sau
  nextStep(
    asset: Assets.PACKAGES_GP_ASSETS_IMAGES_SVG_IC20_FILL_2GEAR_SVG,
    priority: 14,
  ),
  addHandler(
    asset:
        Assets.PACKAGES_GP_ASSETS_IMAGES_SVG_IC20_LINE15_PERSON_ARROWRIGHT_SVG,
    priority: 32,
  ),
  changeHandler(
    asset:
        Assets.PACKAGES_GP_ASSETS_IMAGES_SVG_IC20_LINE15_PERSON_ARROWRIGHT_SVG,
    priority: 37,
  ),
  requestToProvideInfo(
    asset: Assets.PACKAGES_GP_ASSETS_IMAGES_SVG_IC20_LINE15_FILE_SVG,
    priority: 47,
  ),
  moveToOnHold(
    asset: Assets.PACKAGES_GP_ASSETS_IMAGES_SVG_IC20_LINE15_PAUSE_SVG,
    priority: 52,
  ),

  /// Tiếp tục xử lý
  continueHandle(
    asset: Assets
        .PACKAGES_GP_ASSETS_IMAGES_SVG_IC20_FILL_ARROW_HOOK_RIGHT_REDO_SVG,
    priority: 12,
  ),
  approveOnHold(
    asset: Assets.PACKAGES_GP_ASSETS_IMAGES_SVG_IC20_FILL_PAUSE_SVG,
    priority: 7,
  ),
  rejectOnHold(
    asset: Assets.PACKAGES_GP_ASSETS_IMAGES_SVG_IC20_FILL_XMARK_CIRCLE_SVG,
    priority: 8,
  ),
  addFollower(
    asset: Assets.PACKAGES_GP_ASSETS_IMAGES_SVG_IC20_LINE15_EYE_SVG,
    priority: 54,
  ),
  // unfollow(
  //   asset: Assets.PACKAGES_GP_ASSETS_IMAGES_SVG_IC20_LINE15_BELL_SLASH_SVG,
  //   priority: 93,
  // ),
  addLabel(
    asset: Assets.PACKAGES_GP_ASSETS_IMAGES_SVG_IC20_LINE15_TAG_SVG,
    priority: 68,
  ),
  backStep(
    asset: Assets
        .PACKAGES_GP_ASSETS_IMAGES_SVG_IC24_LINE15_ARROW_HOOK_LEFT_UNDO_SVG,
    priority: 72,
  ),
  backEndStep(
    asset: Assets
        .PACKAGES_GP_ASSETS_IMAGES_SVG_IC24_LINE15_ARROW_HOOK_LEFT_UNDO_SVG,
    priority: 73,
  ),
  ;

  const TicketAction({
    this.asset,
    required this.priority,
  });

  final String? asset;

  /// order by `priority` khi hiển thị
  /// 99 = check permission only, không hiển thị button
  final int priority;
}

enum TicketRole {
  requester, // "Người yêu cầu"
  handler, // "Người thực hiện"
  stepManager, // "Người quản trị bước"
  sendToGroup, // "Gửi đến nhóm"
  follower, // "Người theo dõi"
  createdByDepartment, // "Tạo bởi phòng ban"
  supporter // "Người tư vấn / hỗ trợ (người theo dõi)"
}

extension TicketActionListExt on List<TicketAction> {
  List<TicketAction> orderByPriority() {
    return toList()
      ..removeWhere((e) => e.priority == _kUnUsedPriority)
      ..sort((a, b) => a.priority.compareTo(b.priority));
  }

  String printPriority() {
    return map((e) => '$e: ${e.priority}').join(', ');
  }
}

extension TicketActionExt on TicketAction {
  String displayName() {
    switch (this) {
      case TicketAction.addHandler:
        return LocaleKeys.ticket_details_actions_add_handler.tr;
      case TicketAction.addLabel:
        return LocaleKeys.ticket_details_actions_add_label.tr;
      case TicketAction.addFollower:
        return LocaleKeys.ticket_details_actions_add_watcher.tr;
      case TicketAction.approve:
        return LocaleKeys.ticket_details_actions_approve.tr;
      case TicketAction.moveToOnHold:
        return LocaleKeys.ticket_details_actions_archive.tr;
      case TicketAction.cancelTicket:
        return LocaleKeys.ticket_details_actions_cancel.tr;
      case TicketAction.changeHandler:
        return LocaleKeys.ticket_details_actions_change_handler.tr;
      case TicketAction.closeTicket:
        return LocaleKeys.ticket_details_actions_close.tr;
      case TicketAction.duplicate:
        return LocaleKeys.ticket_details_actions_copy.tr;
      case TicketAction.editField:
        return LocaleKeys.ticket_details_actions_edit.tr;
      case TicketAction.nextStep:
        return LocaleKeys.ticket_details_actions_next_step.tr;
      case TicketAction.reject:
        return LocaleKeys.ticket_details_actions_no_approve.tr;
      case TicketAction.rejectOnHold:
        return LocaleKeys.ticket_details_actions_reject_archive.tr;
      case TicketAction.approveOnHold:
        return LocaleKeys.ticket_details_actions_approve_archive.tr;
      case TicketAction.reopenTicket:
        return LocaleKeys.ticket_details_actions_open.tr;
      case TicketAction.requestToProvideInfo:
        return LocaleKeys.ticket_details_actions_provide_info.tr;
      case TicketAction.spam:
        return LocaleKeys.ticket_details_actions_report_spam.tr;
      case TicketAction.chat:
        return LocaleKeys.ticket_details_actions_chat.tr;
      case TicketAction.more:
        return LocaleKeys.ticket_details_actions_more.tr;
      // case TicketAction.unfollow:
      //   return LocaleKeys.ticket_details_actions_unfollow.tr;
      case TicketAction.continueHandle:
        return LocaleKeys.ticket_details_actions_continue_handle.tr;
      case TicketAction.deleteTicket:
        return LocaleKeys.ticket_details_actions_delete.tr;
      case TicketAction.backStep:
        return LocaleKeys.ticket_details_actions_back_step.tr;
      case TicketAction.backEndStep:
        return LocaleKeys.ticket_details_actions_back_last_step.tr;
      default:
        return '';
    }
  }

  Future<TicketActionResult> onClick({
    required BuildContext context,
    required TicketEntity ticketEntity,
    required TicketNodeEntity ticketNodeEntity,
    required TicketFlowChartEntity ticketFlowChartEntity,
    List<WorkflowFormFieldPermissionEntity>? permissions,
    List<TicketAction>? actions,
    List<Assignee>? assignees,
    List<Assignee>? followers,

    /// `action` sử dụng khi bấm vào `TicketAction.more`
    TicketAction? action,
  }) async {
    final TicketActionBottomSheetEntity entity = TicketActionBottomSheetEntity(
      ticketEntity: ticketEntity,
      ticketFlowChartEntity: ticketFlowChartEntity,
      ticketNodeEntity: ticketNodeEntity,
    );

    final currentAction = action ?? this;

    dynamic result;

    switch (currentAction) {
      case TicketAction.more:
        assert(actions != null && actions.length > 1);

        result = await _onClickMore(
          context: context,
          entity: entity,
          actions: actions,
          assignees: assignees,
          permissions: permissions,
          followers: followers,
        );
      case TicketAction.addHandler:
        result = await _onClickAddHandler(entity: entity, assignees: assignees);
      case TicketAction.changeHandler:
        result =
            await _onClickChangeHandler(entity: entity, assignees: assignees);
      case TicketAction.addFollower:
        result = entity.ticketEntity.canAddFollowerAllStep
            ? await _addFollower(entity: entity, followers: followers)
            : await _addFollowerStepOnly(entity: entity);
      case TicketAction.requestToProvideInfo:
        result = await _requestToProvideInfo(context, entity);
      case TicketAction.editField:
        result = await _editTicket(context, entity, permissions: permissions);
      case TicketAction.duplicate:
        result = await _duplicateTicket(context, entity);
      case TicketAction.cancelTicket:
        result = await _cancelTicket(context, entity);
      case TicketAction.chat:
        _openChat(
            ticketEntity: ticketEntity, ticketNodeEntity: ticketNodeEntity);
      case TicketAction.continueHandle:
        result = await _continueHandlingTicket(context, entity);
      case TicketAction.nextStep:
        result = await _updateNodeStatusTicket(context, entity);
      case TicketAction.spam:
        result = await _changeTicketToSpam(context, entity);
      case TicketAction.moveToOnHold:
        result = await _moveToOnHold(context, entity);
      case TicketAction.approveOnHold:
        result = await _approveOnHold(context, entity);
      case TicketAction.rejectOnHold:
        result = await _rejectOnHold(context, entity);
      case TicketAction.approve:
        result = await _approveTicket(context, entity);
      case TicketAction.reject:
        result = await _rejectTicket(context, entity);
      // case TicketAction.unfollow:
      //   result = await _unfollowTicket(context, entity);
      case TicketAction.deleteTicket:
        result = await _deleteTicket(context, entity);
      case TicketAction.closeTicket:
        result = await _closeTicket(context, entity);
      case TicketAction.reopenTicket:
        result = await _reopenTicket(context, entity);
      case TicketAction.addLabel:
        result = await _addLabel(context, entity);
      case TicketAction.backStep:
        result =
            await _backStep(context, entity, BackStepTicketType.previousStep);
      case TicketAction.backEndStep:
        result = await _backStep(context, entity, BackStepTicketType.endStep);
      default:
        logDebug('onClick action $currentAction');
        if (kDebugMode) {
          Popup.instance.showAlert(
            message:
                'Tính năng sẽ được hỗ trợ ở version sau!!! Thông báo ở chế độ dev',
          );
        }
    }

    if (result is TicketActionResult) {
      if (result.result is TicketActionResult) {
        return result.result;
      }

      return TicketActionResult(
        action: result.action,
        result: result,
      );
    } else {
      return TicketActionResult(
        action: currentAction,
        result: result,
      );
    }
  }

  Future<TicketActionResult?> _onClickMore({
    required BuildContext context,
    required TicketActionBottomSheetEntity entity,
    List<TicketAction>? actions,
    List<Assignee>? assignees,
    List<Assignee>? followers,
    List<WorkflowFormFieldPermissionEntity>? permissions,
  }) async {
    // final result = await context.push(
    //   TicketDetailActionRouteData(
    //     entity.ticketEntity.id.toString(),
    //   ).location,
    //   extra: [entity, actions],
    // );

    // ToanNM: lười tạo toJSon() ở các phần entity
    // nên xài tạm modelBottomSheet
    final newActions = (actions ?? []).toList();
    if (newActions.contains(TicketAction.comment)) {
      newActions.remove(TicketAction.comment);
    }

    final result = await Popup.instance.showBottomSheet(
      TicketActionBottomSheet(
        entity: entity,
        actions: newActions,
        canAddFollowerAllStep: entity.ticketEntity.canAddFollowerAllStep,
      ),
    );

    if (result != null && result is TicketActionBottomSheetResult) {
      final action = result.action;

      final ret = await onClick(
          // ignore: use_build_context_synchronously
          context: context,
          ticketEntity: entity.ticketEntity,
          ticketNodeEntity: entity.ticketNodeEntity,
          ticketFlowChartEntity: entity.ticketFlowChartEntity,
          action: action,
          assignees: assignees,
          permissions: permissions,
          followers: followers);

      return TicketActionResult(
        action: action,
        result: ret,
      );
    }

    return null;
  }

  Future<List<AssigneeEntity>> _onClickAddHandler({
    required TicketActionBottomSheetEntity entity,
    List<Assignee>? assignees,
  }) async {
    final isHeadDepartment = entity.ticketNodeEntity.isHeadDepartmentAssignee &&
        entity.ticketEntity.creator?.id == Constants.userId();
    if (isHeadDepartment) {
      return await _pickExistsMembers(
          isShowWsMember: true,
          title: LocaleKeys.ticket_details_actions_add_handler_title.tr);
    }

    final selectedUser = await GetIt.I<TicketNavigator>().toAssigneeListScreen(
      ticketId: entity.ticketEntity.id.toString(),
      nodeId: entity.ticketNodeEntity.id.toString(),
      title: LocaleKeys.ticket_details_actions_add_handler_title.tr,
    );

    if (selectedUser != null) {
      final assigneeEntity =
          _mapper.convert<Assignee, AssigneeEntity>(selectedUser);
      return [assigneeEntity];
    }

    return [];
  }

  Future<List<AssigneeEntity>> _onClickChangeHandler({
    required TicketActionBottomSheetEntity entity,
    List<Assignee>? assignees,
  }) async {
    final isHeadDepartment = entity.ticketNodeEntity.isHeadDepartmentAssignee &&
        entity.ticketEntity.creator?.id == Constants.userId();
    if (isHeadDepartment) {
      return await _pickExistsMembers(
          isShowWsMember: true,
          title: LocaleKeys.ticket_details_actions_change_handler_title.tr);
    }

    final selectedUser = await GetIt.I<TicketNavigator>().toAssigneeListScreen(
      ticketId: entity.ticketEntity.id.toString(),
      nodeId: entity.ticketNodeEntity.id.toString(),
      title: LocaleKeys.ticket_details_actions_change_handler_title.tr,
      ignoreMe: false,
    );

    if (selectedUser != null) {
      final assigneeEntity =
          _mapper.convert<Assignee, AssigneeEntity>(selectedUser);
      return [assigneeEntity];
    }

    return [];
  }

  Future<List> _addFollowerStepOnly({
    required TicketActionBottomSheetEntity entity,
  }) async {
    final List<AssigneeEntity?> assigneeEntities =
        entity.ticketNodeEntity.defaultFollowers?.map((e) => e.info).toList() ??
            [];
    assigneeEntities.removeWhere((e) => e == null);
    final assigneeEntitiesNotNull = List<AssigneeEntity>.from(assigneeEntities);

    final results = await FollowerUtils.pickMembers(
      assigneeEntities: List<AssigneeEntity>.from(assigneeEntitiesNotNull),
      pickerMode: SelectInviteesOptionsMode.selectNewWithValue,
    );

    if (results == null) return [];

    final followerChanges = FollowerUtils.calculateFollowerChanges(
        assigneeEntitiesNotNull, results);

    if (followerChanges.isEmpty) return [];

    // Update the node's followers list
    entity.ticketNodeEntity.defaultFollowers ??= [];
    entity.ticketNodeEntity.defaultFollowers
      ?..clear()
      ..addAll(
        results.map((e) => e.toTicketAssigneeEntity()),
      );

    return [
      followerChanges.toTicketFollowerParams(),
      AddFollowerOption.currentStep,
    ];
  }

  /// hiện lựa chọn add cho bước hoặc cả quy trình
  Future<List> _addFollower({
    required TicketActionBottomSheetEntity entity,
    List<Assignee>? followers,
  }) async {
    final followerEntities =
        _mapper.convertList<Assignee, AssigneeEntity>(followers ?? []);
    final result = await GetIt.I<TicketNavigator>().showBottomSheet(
      child: AddFollowerBottomSheet(
        entity: entity,
        ticketFollowers: followerEntities,
      ),
    );

    if (result is AddFollowerBottomSheetResult &&
        (result.addMembers.isNotEmpty || result.removeMembers.isNotEmpty)) {
      final followerChanges = FollowerChanges(
        addedMembers: result.addMembers,
        removedMembers: result.removeMembers,
      );

      return [
        followerChanges.toTicketFollowerParams(),
        result.option,
      ];
    }

    return [];
  }

  Future<List<AssigneeEntity>> _pickExistsMembers({
    List<Assignee>? assignees,
    List<AssigneeEntity>? selectedEntities,
    bool isShowWsMember = false,
    String? title,
    bool ignoreMe = false,
  }) async {
    final MemberPickerResult<SelectMemberEntity>? result =
        await GetIt.I<MemberPickerWrapper>().pickMember(
      TicketMemberPickerParams.pickExistsMember(
        title: title,
        pickerMode: SelectInviteesOptionsMode.addOnly,
        inputMembers: isShowWsMember ? null : assignees,
        filterOutMemberIds: selectedEntities?.map((e) => e.id).toList(),
        ignoreMe: ignoreMe,
      ),
    );

    final entities = result?.resultEntity?.assigneeEntities ?? [];
    logDebug('_pickExistsMembers entities -> $entities');

    return entities;
  }

  Future<List<AssigneeEntity>> _pickMembers({
    List<AssigneeEntity>? assigneeEntities,
  }) async {
    final MemberPickerResult<SelectMemberEntity>? result =
        await GetIt.I<MemberPickerWrapper>().pickMember(
      TicketMemberPickerParams.pickMember(
        pickerMode: SelectInviteesOptionsMode.selectNewWithValue,
        currentSelectMemberEntity:
            SelectMemberEntity(assigneeEntities: assigneeEntities),
      ),
    );

    final entities = result?.resultEntity?.assigneeEntities ?? [];
    logDebug('_pickMembers entities -> $entities');

    return entities;
  }

  Future<List<dynamic>> _requestToProvideInfo(
    BuildContext context,
    TicketActionBottomSheetEntity entity,
  ) async {
    final result = await GetIt.I<TicketNavigator>()
        .showBottomSheet(child: DetailsRequestToProvideInfoWidget());

    if (result is String) {
      return [
        entity,
        result,
      ];
    }

    return [];
  }

  Future<List<dynamic>> _editTicket(
    BuildContext context,
    TicketActionBottomSheetEntity entity, {
    List<WorkflowFormFieldPermissionEntity>? permissions,
  }) async {
    if (!entity.ticketEntity.canEdit) {
      Popup.instance.showSnackBar(
        message: LocaleKeys.ticket_details_edit_validation_can_not_edit.tr,
        type: SnackbarType.error,
      );
      return [
        entity,
        TicketActionResultType.doNothing,
      ];
    }

    ticketEditDeletedIds.clear();

    final TicketListResponse ticketListResponse =
        _mapper.convert<TicketEntity, TicketListResponse>(entity.ticketEntity);

    await GetIt.I<TicketNavigator>().toEditTicket(
      ticketListResponse: ticketListResponse,
      permissions: permissions,
      nodeEntity: entity.ticketNodeEntity,
    );

    return [
      entity,
      TicketActionResultType.reloadData,
    ];
  }

  Future<List<dynamic>> _duplicateTicket(
    BuildContext context,
    TicketActionBottomSheetEntity entity,
  ) async {
    final TicketListResponse cloneResponse =
        _mapper.convert<TicketEntity, TicketListResponse>(entity.ticketEntity);
    final String oldTitle = cloneResponse.title;
    cloneResponse.title =
        '${LocaleKeys.ticket_details_duplicate_ticket_title.tr}$oldTitle';

    // TODO: còn data bị map lỗi vì hiện tại sử dụng màn create,
    // cần map lại 1 vài data từ formValue -> form.
    final result = await GetIt.I<TicketNavigator>().toCreateTicket(
        mode: TicketCreateMode.duplicate, ticketListResponse: cloneResponse);

    if (result is TicketEntity) {
      return [
        entity,
        TicketActionResultType.doNothing,
      ];
    }

    return [];
  }

  Future _cancelTicket(
    BuildContext context,
    TicketActionBottomSheetEntity entity,
  ) async {
    final result = await GetIt.I<TicketNavigator>().showBottomSheet(
      child: TicketConfirmBottomSheet(
        params: TicketConfirmBottomSheetParams.confirmCancelTicket(
          ticketName: entity.ticketEntity.title,
        ),
      ),
    );

    if (result == true) {
      return entity;
    }
  }

  Future _updateNodeStatusTicket(
    BuildContext context,
    TicketActionBottomSheetEntity entity,
  ) async {
    final result = await GetIt.I<TicketNavigator>().showBottomSheet(
      child: TicketConfirmBottomSheet(
        params: TicketConfirmBottomSheetParams.confirmUpdateTicketNodeStatus(
          nodeName: entity.ticketNodeEntity.option.name ?? '',
        ),
      ),
    );

    if (result == true) {
      return entity;
    }

    return null;
  }

  Future _approveTicket(
    BuildContext context,
    TicketActionBottomSheetEntity entity,
  ) async {
    final result = await GetIt.I<TicketNavigator>().showBottomSheet(
      child: TicketConfirmBottomSheet(
        params: TicketConfirmBottomSheetParams.confirmApproveTicket(
          ticketNode: entity.ticketNodeEntity.option.name ?? '',
        ),
      ),
    );

    if (result == true) {
      return entity;
    }

    return null;
  }

  Future _rejectTicket(
    BuildContext context,
    TicketActionBottomSheetEntity entity,
  ) async {
    final result = await GetIt.I<TicketNavigator>().showBottomSheet(
      child: TicketConfirmBottomSheet(
        params: TicketConfirmBottomSheetParams.confirmRejectTicket(
          ticketNode: entity.ticketNodeEntity.option.name ?? '',
        ),
      ),
    );

    if (result == true) {
      return entity;
    }

    return null;
  }

  Future _continueHandlingTicket(
    BuildContext context,
    TicketActionBottomSheetEntity entity,
  ) async {
    final result = await GetIt.I<TicketNavigator>().showBottomSheet(
      child: TicketConfirmBottomSheet(
        params: TicketConfirmBottomSheetParams.confirmContinueHandling(
          ticketTitle: entity.ticketNodeEntity.option.name ?? '',
        ),
      ),
    );

    if (result == true) {
      return entity;
    }

    return null;
  }

  Future _changeTicketToSpam(
    BuildContext context,
    TicketActionBottomSheetEntity entity,
  ) async {
    final result = await GetIt.I<TicketNavigator>().showBottomSheet(
      child: DetailsChangeToSpamWidget(),
    );

    if (result is String) {
      return [
        entity,
        result,
      ];
    }

    return [];
  }

  Future _moveToOnHold(
    BuildContext context,
    TicketActionBottomSheetEntity entity,
  ) async {
    final result = await GetIt.I<TicketNavigator>().showBottomSheet(
      child: DetailsMoveToOnHoldWidget(),
    );

    if (result is String) {
      return [
        entity,
        result,
      ];
    }

    return null;
  }

  Future _approveOnHold(
    BuildContext context,
    TicketActionBottomSheetEntity entity,
  ) async {
    final result = await GetIt.I<TicketNavigator>().showBottomSheet(
      child: TicketConfirmBottomSheet(
        params: TicketConfirmBottomSheetParams.confirmApproveArchive(
          ticketTitle: entity.ticketEntity.title,
        ),
      ),
    );

    if (result == true) {
      return entity;
    }

    return null;
  }

  Future _rejectOnHold(
    BuildContext context,
    TicketActionBottomSheetEntity entity,
  ) async {
    final result = await GetIt.I<TicketNavigator>().showBottomSheet(
      child: TicketConfirmBottomSheet(
        params: TicketConfirmBottomSheetParams.confirmRejectArchive(
          ticketTitle: entity.ticketEntity.title,
        ),
      ),
    );

    if (result == true) {
      return entity;
    }

    return null;
  }

  Future _unfollowTicket(
    BuildContext context,
    TicketActionBottomSheetEntity entity,
  ) async {
    final result = await GetIt.I<TicketNavigator>().showBottomSheet(
      child: TicketConfirmBottomSheet(
        params: TicketConfirmBottomSheetParams.confirmUnfollowTicket(),
      ),
    );

    if (result == true) {
      return entity;
    }

    return null;
  }

  Future _deleteTicket(
    BuildContext context,
    TicketActionBottomSheetEntity entity,
  ) async {
    final result = await GetIt.I<TicketNavigator>().showBottomSheet(
      child: TicketConfirmBottomSheet(
        params: TicketConfirmBottomSheetParams.confirmDeleteTicket(
          ticketName: entity.ticketEntity.title,
        ),
      ),
    );

    if (result == true) {
      return entity;
    }

    return null;
  }

  Future _closeTicket(
    BuildContext context,
    TicketActionBottomSheetEntity entity,
  ) async {
    final result = await GetIt.I<TicketNavigator>().showBottomSheet(
      child: TicketConfirmBottomSheet(
        params: TicketConfirmBottomSheetParams.confirmCloseTicket(),
      ),
    );

    if (result == true) {
      return entity;
    }

    return null;
  }

  Future _reopenTicket(
    BuildContext context,
    TicketActionBottomSheetEntity entity,
  ) async {
    final result = await GetIt.I<TicketNavigator>().showBottomSheet(
      child: DetailsReopenWidget(),
    );

    if (result is String) {
      return [
        entity,
        result,
      ];
    }

    return null;
  }

  Future _addLabel(
    BuildContext context,
    TicketActionBottomSheetEntity entity,
  ) async {
    final extra = TicketLabelBottomSheetArguments(
      workflowId: entity.ticketEntity.workflowId.toString(),
      selectedTags:
          entity.ticketNodeEntity.ticketTags?.map((e) => e.tag).toList() ?? [],
    ).toMap();

    final result = await GetIt.I<TicketNavigator>().showBottomSheet(
      child: TicketLabelBottomSheet(
        args: TicketLabelBottomSheetArguments.fromMap(extra),
      ),
    );

    if (result is List<WorkflowTagEntity>) {
      return [
        entity,
        result,
      ];
    }

    return null;
  }

  void _openChat({
    required TicketEntity ticketEntity,
    required TicketNodeEntity ticketNodeEntity,
  }) {
    final isTicketRequester = ticketEntity.userRole?.requester ?? false;
    final assignee = ticketNodeEntity.assignee;
    final isNodeAssignee = assignee?.id.toString() == Constants.userId();
    final ticketRequester = ticketEntity.creator;
    if (isTicketRequester && assignee?.id != null) {
      Deeplink.openChatWithUser(
        assignee?.id.toString() ?? "",
      );
      return;
    }
    if (isNodeAssignee && ticketRequester?.id != null) {
      Deeplink.openChatWithUser(
        ticketRequester?.id.toString() ?? "",
      );
      return;
    }
  }

  Future _backStep(
    BuildContext context,
    TicketActionBottomSheetEntity entity,
    BackStepTicketType type,
  ) async {
    final result = await GetIt.I<TicketNavigator>().showBottomSheet(
      child: DetailsBackStepWidget(
        entity: entity,
        type: type,
      ),
    );

    if (result is BackStepResult) {
      return [
        entity,
        result,
      ];
    }

    return null;
  }

  // Future _navigate({
  //   required BuildContext context,
  //   required String location,
  //   Widget Function(BuildContext)? builder,
  //   Object? extra,
  // }) {
  //   /*
  //     case đi từ deeplink:
  //     - sẽ mở `loading_screen` -> `setTicketArguments` method
  //     - lúc này tồn tại cả GetMaterialApp (wrapper) MaterialApp (inside)
  //     - nên nếu sử dụng context từ MaterialApp (có chứa goRouter), nhưng vẫn sẽ không lấy được GoRouter
  //     - nếu đặt tạm 1 biến context ở `TicketMainPage`, sẽ có thể navigate tới screen khi sử dụng context.push,
  //     nhưng page mới sẽ nằm bên dưới page hiện tại trong Stack.
  //   */
  //   if (ticketBuildContext != null && builder != null) {
  //     final widget = builder.call(context);
  //     return Navigator.of(context).push(
  //       MaterialPageRoute(
  //         builder: (context) {
  //           return Material(
  //             // color: Colors.transparent,
  //             shadowColor: Colors.transparent,
  //             child: widget,
  //           );
  //         },
  //         settings: RouteSettings(arguments: extra),
  //       ),
  //     );
  //   } else {
  //     return context.push(location, extra: extra);
  //   }
  // }
}

final class TicketActionResult {
  const TicketActionResult({
    required this.action,
    required this.result,
  });

  final TicketAction action;
  final dynamic result;
}
