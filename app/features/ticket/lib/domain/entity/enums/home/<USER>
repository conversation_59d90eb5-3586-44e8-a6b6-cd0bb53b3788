import 'package:flutter/foundation.dart';
import 'package:gp_core/core.dart';
import 'package:gp_feat_ticket/constants/vars/ticket_filter.constants.dart';
import 'package:jiffy/jiffy.dart';

import '../../../../data/model/request/ticket/list/ticket_list_params.dart';
import '../enums.dart';

enum TicketHomeTabs {
  /// Tất cả
  all,

  /// chờ xử lý
  waitingForHandling,

  /// đang xử lý
  handling,

  /// đã xử lý
  handled,

  /// không duyệt
  notApprove,

  /// đã duyệt
  approved,

  /// chờ bổ sung
  waitingForProvideInfo,

  /// Chờ duyệt bảo lưu
  waitingForArchive,

  /// bảo lưu
  archived,

  /// đã hủy
  cancelled,

  /// đã đóng
  closed,

  /// Spam
  spam,

  /// Quá hạn
  outOfDate
}

abstract class HomeTabEntity {
  List<TicketHomeTabs> get tabs;

  int itemCounts = 0;

  TicketHomeMenuEnums get homeMenuEnums;

  String mapTicketHomeTabsToString(TicketHomeTabs tab);

  TicketListParams mapTicketHomeTabsToParams(TicketHomeTabs tab);

  final now = DateTime.now();
  late final monthAgo = Jiffy.parseFromDateTime(now)
      .subtract(months: TicketFilterConstants.defaultFilterMonth)
      .dateTime;

  late final TicketListParams defaultParams = TicketListParams(
    startTime: DateFormat('dd/MM/yyyy').format(monthAgo),
    endTime: DateFormat('dd/MM/yyyy').format(now),
    priorities: TicketPriority.values.map((e) => e.value).toList(),
    homeMenuEnum: homeMenuEnums,
  );
}

final class HomeSendToMeTabEntity extends HomeTabEntity {
  @override
  List<TicketHomeTabs> get tabs => [
        TicketHomeTabs.handling,
        // TicketHomeTabs.waitingForHandling,
        TicketHomeTabs.handled,
        TicketHomeTabs.notApprove,
        TicketHomeTabs.approved,
        TicketHomeTabs.waitingForProvideInfo,
        TicketHomeTabs.waitingForArchive,
        TicketHomeTabs.archived,
        TicketHomeTabs.cancelled,
        TicketHomeTabs.outOfDate,
        TicketHomeTabs.spam,
        TicketHomeTabs.all,
      ];

  @override
  TicketHomeMenuEnums get homeMenuEnums => TicketHomeMenuEnums.sendToMe;

  @override
  String mapTicketHomeTabsToString(TicketHomeTabs tab) {
    switch (tab) {
      case TicketHomeTabs.all:
        return LocaleKeys.ticket_home_menu_tabs_send_to_me_all.tr;
      case TicketHomeTabs.waitingForHandling:
        return LocaleKeys
            .ticket_home_menu_tabs_send_to_me_waiting_for_handling.tr;
      case TicketHomeTabs.handling:
        return LocaleKeys.ticket_home_menu_tabs_send_to_me_handling.tr;
      case TicketHomeTabs.handled:
        return LocaleKeys.ticket_home_menu_tabs_send_to_me_handled.tr;
      case TicketHomeTabs.notApprove:
        return LocaleKeys.ticket_home_menu_tabs_send_to_me_not_approve.tr;
      case TicketHomeTabs.approved:
        return LocaleKeys.ticket_home_menu_tabs_send_to_me_approved.tr;
      case TicketHomeTabs.waitingForProvideInfo:
        return LocaleKeys
            .ticket_home_menu_tabs_send_to_me_waiting_for_provide_info.tr;
      case TicketHomeTabs.waitingForArchive:
        return LocaleKeys
            .ticket_home_menu_tabs_send_to_me_waiting_for_archive.tr;
      case TicketHomeTabs.cancelled:
        return LocaleKeys.ticket_home_menu_tabs_send_to_me_cancelled.tr;
      case TicketHomeTabs.archived:
        return LocaleKeys.ticket_home_menu_tabs_send_to_me_archived.tr;
      case TicketHomeTabs.outOfDate:
        return LocaleKeys.ticket_home_menu_tabs_send_to_me_out_of_date.tr;
      case TicketHomeTabs.spam:
        return LocaleKeys.ticket_home_menu_tabs_send_to_me_spam.tr;
      default:
        logDebug("_mapTicketHomeTabsToString error for ${tab.name}");
        return '';
    }
  }

  @override
  TicketListParams mapTicketHomeTabsToParams(TicketHomeTabs tab) {
    final params = defaultParams;
    switch (tab) {
      case TicketHomeTabs.all:
        return params.copyWith(
          nodeStatuses: [
            TicketNodeStatus.waitingForHandling.value,
            TicketNodeStatus.handling.value,
            TicketNodeStatus.handled.value,
            TicketNodeStatus.notApprove.value,
            TicketNodeStatus.approved.value,
            TicketNodeStatus.waitingForProvideInfo.value,
            TicketNodeStatus.waitingForArchive.value,
            TicketNodeStatus.archived.value,
            TicketNodeStatus.cancelled.value,
            TicketNodeStatus.spam.value,
          ],
          ticketStatuses: [
            TicketStatus.waitingForHandling.value,
            TicketStatus.handling.value,
            TicketStatus.archived.value,
            TicketStatus.cancelled.value,
            TicketStatus.handled.value,
            TicketStatus.closed.value,
            TicketStatus.spam.value,
          ],
        );
      case TicketHomeTabs.waitingForHandling:
        return params.copyWith(
          nodeStatuses: [
            TicketNodeStatus.waitingForHandling.value,
          ],
          ticketStatuses: [
            TicketStatus.waitingForHandling.value,
            TicketStatus.handling.value,
          ],
        );

      case TicketHomeTabs.handling:
        return params.copyWith(
          nodeStatuses: [
            TicketNodeStatus.handling.value,
          ],
          ticketStatuses: [
            TicketStatus.waitingForHandling.value,
            TicketStatus.handling.value,
            TicketStatus.archived.value,
            TicketStatus.cancelled.value,
            TicketStatus.handled.value,
            TicketStatus.closed.value,
            TicketStatus.spam.value,
          ],
        );
      case TicketHomeTabs.handled:
        return params.copyWith(
          nodeStatuses: [
            TicketNodeStatus.handled.value,
          ],
          ticketStatuses: [
            TicketStatus.waitingForHandling.value,
            TicketStatus.handling.value,
            TicketStatus.archived.value,
            TicketStatus.cancelled.value,
            TicketStatus.handled.value,
            TicketStatus.closed.value,
            TicketStatus.spam.value,
          ],
        );
      case TicketHomeTabs.notApprove:
        return params.copyWith(
          nodeStatuses: [
            TicketNodeStatus.notApprove.value,
          ],
          ticketStatuses: [
            TicketStatus.waitingForHandling.value,
            TicketStatus.handling.value,
            TicketStatus.archived.value,
            TicketStatus.cancelled.value,
            TicketStatus.handled.value,
            TicketStatus.closed.value,
            TicketStatus.spam.value,
          ],
        );
      case TicketHomeTabs.approved:
        return params.copyWith(
          nodeStatuses: [
            TicketNodeStatus.approved.value,
          ],
          ticketStatuses: [
            TicketStatus.waitingForHandling.value,
            TicketStatus.handling.value,
            TicketStatus.archived.value,
            TicketStatus.cancelled.value,
            TicketStatus.handled.value,
            TicketStatus.closed.value,
            TicketStatus.spam.value,
          ],
        );
      case TicketHomeTabs.waitingForProvideInfo:
        return params.copyWith(
          nodeStatuses: [
            TicketNodeStatus.waitingForProvideInfo.value,
          ],
          ticketStatuses: [
            TicketStatus.waitingForHandling.value,
            TicketStatus.handling.value,
            TicketStatus.archived.value,
            TicketStatus.cancelled.value,
            TicketStatus.handled.value,
            TicketStatus.closed.value,
            TicketStatus.spam.value,
          ],
        );
      case TicketHomeTabs.waitingForArchive:
        return params.copyWith(
          nodeStatuses: [
            TicketNodeStatus.waitingForArchive.value,
          ],
          ticketStatuses: [
            TicketStatus.waitingForHandling.value,
            TicketStatus.handling.value,
            TicketStatus.archived.value,
            TicketStatus.cancelled.value,
            TicketStatus.handled.value,
            TicketStatus.closed.value,
            TicketStatus.spam.value,
          ],
        );
      case TicketHomeTabs.cancelled:
        return params.copyWith(
          nodeStatuses: [
            TicketNodeStatus.cancelled.value,
          ],
          ticketStatuses: [
            TicketStatus.waitingForHandling.value,
            TicketStatus.handling.value,
            TicketStatus.archived.value,
            TicketStatus.cancelled.value,
            TicketStatus.handled.value,
            TicketStatus.closed.value,
            TicketStatus.spam.value,
          ],
        );
      case TicketHomeTabs.archived:
        return params.copyWith(
          nodeStatuses: [
            TicketNodeStatus.archived.value,
          ],
          ticketStatuses: [
            TicketStatus.waitingForHandling.value,
            TicketStatus.handling.value,
            TicketStatus.archived.value,
            TicketStatus.cancelled.value,
            TicketStatus.handled.value,
            TicketStatus.closed.value,
            TicketStatus.spam.value,
          ],
        );
      case TicketHomeTabs.outOfDate:
        return params.copyWith(
          nodeStatuses: [
            // TicketNodeStatus.waitingForHandling.value,
            TicketNodeStatus.handling.value,
          ],
          ticketStatuses: [
            TicketStatus.waitingForHandling.value,
            TicketStatus.handling.value,
            TicketStatus.archived.value,
            TicketStatus.cancelled.value,
            TicketStatus.handled.value,
            TicketStatus.closed.value,
            TicketStatus.spam.value,
          ],
          isOverdue: true,
        );
      case TicketHomeTabs.spam:
        return params.copyWith(
          nodeStatuses: [
            TicketNodeStatus.spam.value,
          ],
          ticketStatuses: [
            TicketStatus.waitingForHandling.value,
            TicketStatus.handling.value,
            TicketStatus.archived.value,
            TicketStatus.cancelled.value,
            TicketStatus.handled.value,
            TicketStatus.closed.value,
            TicketStatus.spam.value,
          ],
        );
      default:
        logDebug("mapTicketHomeTabsToParams missing for ${tab.name}");
        return params;
    }
  }
}

final class HomeCreatedByMeTabEntity extends HomeTabEntity {
  @override
  TicketHomeMenuEnums get homeMenuEnums => TicketHomeMenuEnums.createdByMe;

  @override
  List<TicketHomeTabs> get tabs => [
        TicketHomeTabs.waitingForHandling,
        TicketHomeTabs.handling,
        TicketHomeTabs.archived,
        TicketHomeTabs.cancelled,
        TicketHomeTabs.handled,
        TicketHomeTabs.closed,
        TicketHomeTabs.outOfDate,
        TicketHomeTabs.spam,
        TicketHomeTabs.all,
      ];

  @override
  String mapTicketHomeTabsToString(TicketHomeTabs tab) {
    switch (tab) {
      case TicketHomeTabs.all:
        return LocaleKeys.ticket_home_menu_tabs_created_by_me_all.tr;
      case TicketHomeTabs.waitingForHandling:
        return LocaleKeys
            .ticket_home_menu_tabs_created_by_me_waiting_for_handling.tr;
      case TicketHomeTabs.handling:
        return LocaleKeys.ticket_home_menu_tabs_created_by_me_handling.tr;
      case TicketHomeTabs.archived:
        return LocaleKeys.ticket_home_menu_tabs_created_by_me_archived.tr;
      case TicketHomeTabs.cancelled:
        return LocaleKeys.ticket_home_menu_tabs_created_by_me_cancelled.tr;
      case TicketHomeTabs.handled:
        return LocaleKeys.ticket_home_menu_tabs_created_by_me_handled.tr;
      case TicketHomeTabs.closed:
        return LocaleKeys.ticket_home_menu_tabs_created_by_me_closed.tr;
      case TicketHomeTabs.outOfDate:
        return LocaleKeys.ticket_home_menu_tabs_created_by_me_out_of_date.tr;
      case TicketHomeTabs.spam:
        return LocaleKeys.ticket_home_menu_tabs_created_by_me_spam.tr;
      default:
        logDebug("_mapTicketHomeTabsToString error for ${tab.name}");
        return '';
    }
  }

  @override
  TicketListParams mapTicketHomeTabsToParams(TicketHomeTabs tab) {
    final params = defaultParams;
    switch (tab) {
      case TicketHomeTabs.all:
        return params.copyWith(
          ticketStatuses: [
            TicketStatus.waitingForHandling.value,
            TicketStatus.handling.value,
            TicketStatus.archived.value,
            TicketStatus.cancelled.value,
            TicketStatus.handled.value,
            TicketStatus.closed.value,
            TicketStatus.spam.value,
          ],
        );
      case TicketHomeTabs.waitingForHandling:
        return params.copyWith(
          ticketStatuses: [
            TicketStatus.waitingForHandling.value,
          ],
        );

      case TicketHomeTabs.handling:
        return params.copyWith(
          ticketStatuses: [
            TicketStatus.handling.value,
          ],
        );
      case TicketHomeTabs.archived:
        return params.copyWith(
          ticketStatuses: [
            TicketStatus.archived.value,
          ],
        );
      case TicketHomeTabs.cancelled:
        return params.copyWith(
          ticketStatuses: [
            TicketStatus.cancelled.value,
          ],
        );
      case TicketHomeTabs.handled:
        return params.copyWith(
          ticketStatuses: [
            TicketStatus.handled.value,
          ],
        );
      case TicketHomeTabs.closed:
        return params.copyWith(
          ticketStatuses: [
            TicketStatus.closed.value,
          ],
        );
      case TicketHomeTabs.outOfDate:
        return params.copyWith(
          ticketStatuses: [
            TicketStatus.waitingForHandling.value,
            TicketStatus.handling.value,
          ],
          isOverdue: true,
        );
      case TicketHomeTabs.spam:
        return params.copyWith(
          ticketStatuses: [
            TicketStatus.spam.value,
          ],
        );
      default:
        logDebug("mapTicketHomeTabsToParams missing for ${tab.name}");
        if (kDebugMode) {
          throw Exception("mapTicketHomeTabsToParams missing for ${tab.name}");
        }
        return params;
    }
  }
}

final class HomeFollowTabEntity extends HomeTabEntity {
  @override
  TicketHomeMenuEnums get homeMenuEnums => TicketHomeMenuEnums.follow;

  @override
  List<TicketHomeTabs> get tabs => [
        TicketHomeTabs.waitingForHandling,
        TicketHomeTabs.handling,
        TicketHomeTabs.archived,
        TicketHomeTabs.cancelled,
        TicketHomeTabs.handled,
        TicketHomeTabs.closed,
        TicketHomeTabs.outOfDate,
        TicketHomeTabs.spam,
        TicketHomeTabs.all,
      ];

  @override
  String mapTicketHomeTabsToString(TicketHomeTabs tab) {
    switch (tab) {
      case TicketHomeTabs.all:
        return LocaleKeys.ticket_home_menu_tabs_follow_all.tr;
      case TicketHomeTabs.waitingForHandling:
        return LocaleKeys.ticket_home_menu_tabs_follow_waiting_for_handling.tr;
      case TicketHomeTabs.handling:
        return LocaleKeys.ticket_home_menu_tabs_follow_handling.tr;
      case TicketHomeTabs.handled:
        return LocaleKeys.ticket_home_menu_tabs_follow_handled.tr;
      case TicketHomeTabs.archived:
        return LocaleKeys.ticket_home_menu_tabs_follow_archived.tr;
      case TicketHomeTabs.cancelled:
        return LocaleKeys.ticket_home_menu_tabs_follow_cancelled.tr;
      case TicketHomeTabs.closed:
        return LocaleKeys.ticket_home_menu_tabs_follow_closed.tr;
      case TicketHomeTabs.outOfDate:
        return LocaleKeys.ticket_home_menu_tabs_follow_out_of_date.tr;
      case TicketHomeTabs.spam:
        return LocaleKeys.ticket_home_menu_tabs_follow_spam.tr;
      default:
        logDebug("_mapTicketHomeTabsToString error for ${tab.name}");
        return '';
    }
  }

  @override
  TicketListParams mapTicketHomeTabsToParams(TicketHomeTabs tab) {
    final params = defaultParams;
    switch (tab) {
      case TicketHomeTabs.all:
        return params.copyWith(
          ticketStatuses: [
            TicketStatus.waitingForHandling.value,
            TicketStatus.handling.value,
            TicketStatus.archived.value,
            TicketStatus.cancelled.value,
            TicketStatus.handled.value,
            TicketStatus.closed.value,
            TicketStatus.spam.value,
          ],
        );
      case TicketHomeTabs.waitingForHandling:
        return params.copyWith(
          ticketStatuses: [
            TicketStatus.waitingForHandling.value,
          ],
        );

      case TicketHomeTabs.handling:
        return params.copyWith(
          ticketStatuses: [
            TicketStatus.handling.value,
          ],
        );
      case TicketHomeTabs.archived:
        return params.copyWith(
          ticketStatuses: [
            TicketStatus.archived.value,
          ],
        );
      case TicketHomeTabs.cancelled:
        return params.copyWith(
          ticketStatuses: [
            TicketStatus.cancelled.value,
          ],
        );
      case TicketHomeTabs.handled:
        return params.copyWith(
          ticketStatuses: [
            TicketStatus.handled.value,
          ],
        );
      case TicketHomeTabs.closed:
        return params.copyWith(
          ticketStatuses: [
            TicketStatus.closed.value,
          ],
        );
      case TicketHomeTabs.outOfDate:
        return params.copyWith(
          ticketStatuses: [
            TicketStatus.waitingForHandling.value,
            TicketStatus.handling.value,
          ],
          isOverdue: true,
        );
      case TicketHomeTabs.spam:
        return params.copyWith(
          ticketStatuses: [
            TicketStatus.spam.value,
          ],
        );
      default:
        logDebug("mapTicketHomeTabsToParams missing for ${tab.name}");
        if (kDebugMode) {
          throw Exception("mapTicketHomeTabsToParams missing for ${tab.name}");
        }
        return params;
    }
  }
}

final class HomeSendToMyAssigneeTabEntity extends HomeTabEntity {
  @override
  TicketHomeMenuEnums get homeMenuEnums => TicketHomeMenuEnums.sendToMyAssignee;

  @override
  List<TicketHomeTabs> get tabs => [
        TicketHomeTabs.waitingForHandling,
        TicketHomeTabs.handling,
        TicketHomeTabs.handled,
        TicketHomeTabs.notApprove,
        TicketHomeTabs.approved,
        TicketHomeTabs.waitingForProvideInfo,
        TicketHomeTabs.waitingForArchive,
        TicketHomeTabs.archived,
        TicketHomeTabs.cancelled,
        TicketHomeTabs.outOfDate,
        TicketHomeTabs.spam,
        TicketHomeTabs.all,
      ];

  @override
  String mapTicketHomeTabsToString(TicketHomeTabs tab) {
    switch (tab) {
      case TicketHomeTabs.all:
        return LocaleKeys.ticket_home_menu_tabs_send_to_my_assignee_all.tr;
      case TicketHomeTabs.waitingForHandling:
        return LocaleKeys
            .ticket_home_menu_tabs_send_to_my_assignee_waiting_for_handling.tr;
      case TicketHomeTabs.handling:
        return LocaleKeys.ticket_home_menu_tabs_send_to_my_assignee_handling.tr;
      case TicketHomeTabs.handled:
        return LocaleKeys.ticket_home_menu_tabs_send_to_my_assignee_handled.tr;
      case TicketHomeTabs.notApprove:
        return LocaleKeys
            .ticket_home_menu_tabs_send_to_my_assignee_not_approve.tr;
      case TicketHomeTabs.approved:
        return LocaleKeys.ticket_home_menu_tabs_send_to_my_assignee_approved.tr;
      case TicketHomeTabs.waitingForProvideInfo:
        return LocaleKeys
            .ticket_home_menu_tabs_send_to_my_assignee_waiting_for_provide_info
            .tr;
      case TicketHomeTabs.waitingForArchive:
        return LocaleKeys
            .ticket_home_menu_tabs_send_to_my_assignee_waiting_for_archive.tr;
      case TicketHomeTabs.archived:
        return LocaleKeys.ticket_home_menu_tabs_send_to_my_assignee_archived.tr;
      case TicketHomeTabs.cancelled:
        return LocaleKeys
            .ticket_home_menu_tabs_send_to_my_assignee_cancelled.tr;
      case TicketHomeTabs.outOfDate:
        return LocaleKeys
            .ticket_home_menu_tabs_send_to_my_assignee_out_of_date.tr;
      case TicketHomeTabs.spam:
        return LocaleKeys.ticket_home_menu_tabs_send_to_my_assignee_spam.tr;
      default:
        logDebug("_mapTicketHomeTabsToString error for ${tab.name}");
        return '';
    }
  }

  @override
  TicketListParams mapTicketHomeTabsToParams(TicketHomeTabs tab) {
    final params = defaultParams;
    switch (tab) {
      case TicketHomeTabs.all:
        return params.copyWith(
          nodeStatuses: [
            TicketNodeStatus.waitingForHandling.value,
            TicketNodeStatus.handling.value,
            TicketNodeStatus.handled.value,
            TicketNodeStatus.notApprove.value,
            TicketNodeStatus.approved.value,
            TicketNodeStatus.waitingForProvideInfo.value,
            TicketNodeStatus.waitingForArchive.value,
            TicketNodeStatus.archived.value,
            TicketNodeStatus.cancelled.value,
            TicketNodeStatus.spam.value,
          ],
          ticketStatuses: [
            TicketStatus.waitingForHandling.value,
            TicketStatus.handling.value,
            TicketStatus.archived.value,
            TicketStatus.cancelled.value,
            TicketStatus.handled.value,
            TicketStatus.closed.value,
            TicketStatus.spam.value,
          ],
        );
      case TicketHomeTabs.waitingForHandling:
        return params.copyWith(
          nodeStatuses: [
            TicketNodeStatus.waitingForHandling.value,
          ],
          ticketStatuses: [
            TicketStatus.waitingForHandling.value,
            TicketStatus.handling.value,
            TicketStatus.archived.value,
            TicketStatus.cancelled.value,
            TicketStatus.handled.value,
            TicketStatus.closed.value,
            TicketStatus.spam.value,
          ],
        );

      case TicketHomeTabs.handling:
        return params.copyWith(
          nodeStatuses: [
            TicketNodeStatus.handling.value,
          ],
          ticketStatuses: [
            TicketStatus.waitingForHandling.value,
            TicketStatus.handling.value,
            TicketStatus.archived.value,
            TicketStatus.cancelled.value,
            TicketStatus.handled.value,
            TicketStatus.closed.value,
            TicketStatus.spam.value,
          ],
        );
      case TicketHomeTabs.handled:
        return params.copyWith(
          nodeStatuses: [
            TicketNodeStatus.handled.value,
          ],
          ticketStatuses: [
            TicketStatus.waitingForHandling.value,
            TicketStatus.handling.value,
            TicketStatus.archived.value,
            TicketStatus.cancelled.value,
            TicketStatus.handled.value,
            TicketStatus.closed.value,
            TicketStatus.spam.value,
          ],
        );
      case TicketHomeTabs.notApprove:
        return params.copyWith(
          nodeStatuses: [
            TicketNodeStatus.notApprove.value,
          ],
          ticketStatuses: [
            TicketStatus.waitingForHandling.value,
            TicketStatus.handling.value,
            TicketStatus.archived.value,
            TicketStatus.cancelled.value,
            TicketStatus.handled.value,
            TicketStatus.closed.value,
            TicketStatus.spam.value,
          ],
        );
      case TicketHomeTabs.approved:
        return params.copyWith(
          nodeStatuses: [
            TicketNodeStatus.approved.value,
          ],
          ticketStatuses: [
            TicketStatus.waitingForHandling.value,
            TicketStatus.handling.value,
            TicketStatus.archived.value,
            TicketStatus.cancelled.value,
            TicketStatus.handled.value,
            TicketStatus.closed.value,
            TicketStatus.spam.value,
          ],
        );
      case TicketHomeTabs.waitingForProvideInfo:
        return params.copyWith(
          nodeStatuses: [
            TicketNodeStatus.waitingForProvideInfo.value,
          ],
          ticketStatuses: [
            TicketStatus.waitingForHandling.value,
            TicketStatus.handling.value,
            TicketStatus.archived.value,
            TicketStatus.cancelled.value,
            TicketStatus.handled.value,
            TicketStatus.closed.value,
            TicketStatus.spam.value,
          ],
        );
      case TicketHomeTabs.waitingForArchive:
        return params.copyWith(
          nodeStatuses: [
            TicketNodeStatus.waitingForArchive.value,
          ],
          ticketStatuses: [
            TicketStatus.waitingForHandling.value,
            TicketStatus.handling.value,
            TicketStatus.archived.value,
            TicketStatus.cancelled.value,
            TicketStatus.handled.value,
            TicketStatus.closed.value,
            TicketStatus.spam.value,
          ],
        );
      case TicketHomeTabs.cancelled:
        return params.copyWith(
          nodeStatuses: [
            TicketNodeStatus.cancelled.value,
          ],
          ticketStatuses: [
            TicketStatus.waitingForHandling.value,
            TicketStatus.handling.value,
            TicketStatus.archived.value,
            TicketStatus.cancelled.value,
            TicketStatus.handled.value,
            TicketStatus.closed.value,
            TicketStatus.spam.value,
          ],
        );
      case TicketHomeTabs.archived:
        return params.copyWith(
          nodeStatuses: [
            TicketNodeStatus.archived.value,
          ],
          ticketStatuses: [
            TicketStatus.waitingForHandling.value,
            TicketStatus.handling.value,
            TicketStatus.archived.value,
            TicketStatus.cancelled.value,
            TicketStatus.handled.value,
            TicketStatus.closed.value,
            TicketStatus.spam.value,
          ],
        );
      case TicketHomeTabs.outOfDate:
        return params.copyWith(
          nodeStatuses: [
            TicketNodeStatus.waitingForHandling.value,
            // TicketNodeStatus.handling.value,
          ],
          ticketStatuses: [
            TicketStatus.waitingForHandling.value,
            TicketStatus.handling.value,
            TicketStatus.archived.value,
            TicketStatus.cancelled.value,
            TicketStatus.handled.value,
            TicketStatus.closed.value,
            TicketStatus.spam.value,
          ],
          isOverdue: true,
        );
      case TicketHomeTabs.spam:
        return params.copyWith(
          nodeStatuses: [
            TicketNodeStatus.spam.value,
          ],
          ticketStatuses: [
            TicketStatus.waitingForHandling.value,
            TicketStatus.handling.value,
            TicketStatus.archived.value,
            TicketStatus.cancelled.value,
            TicketStatus.handled.value,
            TicketStatus.closed.value,
            TicketStatus.spam.value,
          ],
        );
      default:
        logDebug("mapTicketHomeTabsToParams missing for ${tab.name}");
        if (kDebugMode) {
          throw Exception("mapTicketHomeTabsToParams missing for ${tab.name}");
        }
        return params;
    }
  }
}

final class HomeSendToMyGroupEntity extends HomeTabEntity {
  @override
  TicketHomeMenuEnums get homeMenuEnums => TicketHomeMenuEnums.sendToMyGroup;

  @override
  List<TicketHomeTabs> get tabs => [
        // TicketHomeTabs.waitingForHandling,
        TicketHomeTabs.handling,
        TicketHomeTabs.handled,
        TicketHomeTabs.notApprove,
        TicketHomeTabs.approved,
        TicketHomeTabs.waitingForProvideInfo,
        TicketHomeTabs.waitingForArchive,
        TicketHomeTabs.archived,
        TicketHomeTabs.cancelled,
        TicketHomeTabs.outOfDate,
        TicketHomeTabs.spam,
        TicketHomeTabs.all,
      ];

  @override
  String mapTicketHomeTabsToString(TicketHomeTabs tab) {
    switch (tab) {
      case TicketHomeTabs.all:
        return LocaleKeys.ticket_home_menu_tabs_send_to_my_group_all.tr;
      case TicketHomeTabs.waitingForHandling:
        return LocaleKeys
            .ticket_home_menu_tabs_send_to_my_group_waiting_for_handling.tr;
      case TicketHomeTabs.handling:
        return LocaleKeys.ticket_home_menu_tabs_send_to_my_group_handling.tr;
      case TicketHomeTabs.handled:
        return LocaleKeys.ticket_home_menu_tabs_send_to_my_group_handled.tr;
      case TicketHomeTabs.notApprove:
        return LocaleKeys.ticket_home_menu_tabs_send_to_my_group_not_approve.tr;
      case TicketHomeTabs.approved:
        return LocaleKeys.ticket_home_menu_tabs_send_to_my_group_approved.tr;
      case TicketHomeTabs.waitingForProvideInfo:
        return LocaleKeys
            .ticket_home_menu_tabs_send_to_my_group_waiting_for_provide_info.tr;
      case TicketHomeTabs.waitingForArchive:
        return LocaleKeys
            .ticket_home_menu_tabs_send_to_my_group_waiting_for_archive.tr;
      case TicketHomeTabs.archived:
        return LocaleKeys.ticket_home_menu_tabs_send_to_my_group_archived.tr;
      case TicketHomeTabs.cancelled:
        return LocaleKeys.ticket_home_menu_tabs_send_to_my_group_cancelled.tr;
      case TicketHomeTabs.outOfDate:
        return LocaleKeys.ticket_home_menu_tabs_send_to_my_group_out_of_date.tr;
      case TicketHomeTabs.spam:
        return LocaleKeys.ticket_home_menu_tabs_send_to_my_group_spam.tr;
      default:
        logDebug("_mapTicketHomeTabsToString error for ${tab.name}");
        return '';
    }
  }

  @override
  TicketListParams mapTicketHomeTabsToParams(TicketHomeTabs tab) {
    final params = defaultParams;
    switch (tab) {
      case TicketHomeTabs.all:
        return params.copyWith(
          nodeStatuses: [
            // TicketNodeStatus.waitingForHandling.value,
            TicketNodeStatus.handling.value,
            TicketNodeStatus.handled.value,
            TicketNodeStatus.notApprove.value,
            TicketNodeStatus.approved.value,
            TicketNodeStatus.waitingForProvideInfo.value,
            TicketNodeStatus.waitingForArchive.value,
            TicketNodeStatus.archived.value,
            TicketNodeStatus.cancelled.value,
            TicketNodeStatus.spam.value,
          ],
          ticketStatuses: [
            TicketStatus.waitingForHandling.value,
            TicketStatus.handling.value,
            TicketStatus.archived.value,
            TicketStatus.cancelled.value,
            TicketStatus.handled.value,
            TicketStatus.closed.value,
            TicketStatus.spam.value,
          ],
        );
      case TicketHomeTabs.waitingForHandling:
        return params.copyWith(
          nodeStatuses: [
            TicketNodeStatus.waitingForHandling.value,
          ],
          ticketStatuses: [
            TicketStatus.waitingForHandling.value,
            TicketStatus.handling.value,
            TicketStatus.archived.value,
            TicketStatus.cancelled.value,
            TicketStatus.handled.value,
            TicketStatus.closed.value,
            TicketStatus.spam.value,
          ],
        );

      case TicketHomeTabs.handling:
        return params.copyWith(
          nodeStatuses: [
            TicketNodeStatus.handling.value,
          ],
          ticketStatuses: [
            TicketStatus.waitingForHandling.value,
            TicketStatus.handling.value,
            TicketStatus.archived.value,
            TicketStatus.cancelled.value,
            TicketStatus.handled.value,
            TicketStatus.closed.value,
            TicketStatus.spam.value,
          ],
        );
      case TicketHomeTabs.handled:
        return params.copyWith(
          nodeStatuses: [
            TicketNodeStatus.handled.value,
          ],
          ticketStatuses: [
            TicketStatus.waitingForHandling.value,
            TicketStatus.handling.value,
            TicketStatus.archived.value,
            TicketStatus.cancelled.value,
            TicketStatus.handled.value,
            TicketStatus.closed.value,
            TicketStatus.spam.value,
          ],
        );
      case TicketHomeTabs.notApprove:
        return params.copyWith(
          nodeStatuses: [
            TicketNodeStatus.notApprove.value,
          ],
          ticketStatuses: [
            TicketStatus.waitingForHandling.value,
            TicketStatus.handling.value,
            TicketStatus.archived.value,
            TicketStatus.cancelled.value,
            TicketStatus.handled.value,
            TicketStatus.closed.value,
            TicketStatus.spam.value,
          ],
        );
      case TicketHomeTabs.approved:
        return params.copyWith(
          nodeStatuses: [
            TicketNodeStatus.approved.value,
          ],
          ticketStatuses: [
            TicketStatus.waitingForHandling.value,
            TicketStatus.handling.value,
            TicketStatus.archived.value,
            TicketStatus.cancelled.value,
            TicketStatus.handled.value,
            TicketStatus.closed.value,
            TicketStatus.spam.value,
          ],
        );
      case TicketHomeTabs.waitingForProvideInfo:
        return params.copyWith(
          nodeStatuses: [
            TicketNodeStatus.waitingForProvideInfo.value,
          ],
          ticketStatuses: [
            TicketStatus.waitingForHandling.value,
            TicketStatus.handling.value,
            TicketStatus.archived.value,
            TicketStatus.cancelled.value,
            TicketStatus.handled.value,
            TicketStatus.closed.value,
            TicketStatus.spam.value,
          ],
        );
      case TicketHomeTabs.waitingForArchive:
        return params.copyWith(
          nodeStatuses: [
            TicketNodeStatus.waitingForArchive.value,
          ],
          ticketStatuses: [
            TicketStatus.waitingForHandling.value,
            TicketStatus.handling.value,
            TicketStatus.archived.value,
            TicketStatus.cancelled.value,
            TicketStatus.handled.value,
            TicketStatus.closed.value,
            TicketStatus.spam.value,
          ],
        );
      case TicketHomeTabs.cancelled:
        return params.copyWith(
          nodeStatuses: [
            TicketNodeStatus.cancelled.value,
          ],
          ticketStatuses: [
            TicketStatus.waitingForHandling.value,
            TicketStatus.handling.value,
            TicketStatus.archived.value,
            TicketStatus.cancelled.value,
            TicketStatus.handled.value,
            TicketStatus.closed.value,
            TicketStatus.spam.value,
          ],
        );
      case TicketHomeTabs.archived:
        return params.copyWith(
          nodeStatuses: [
            TicketNodeStatus.archived.value,
          ],
          ticketStatuses: [
            TicketStatus.waitingForHandling.value,
            TicketStatus.handling.value,
            TicketStatus.archived.value,
            TicketStatus.cancelled.value,
            TicketStatus.handled.value,
            TicketStatus.closed.value,
            TicketStatus.spam.value,
          ],
        );
      case TicketHomeTabs.outOfDate:
        return params.copyWith(
          nodeStatuses: [
            TicketNodeStatus.waitingForHandling.value,
            // TicketNodeStatus.handling.value,
          ],
          ticketStatuses: [
            TicketStatus.waitingForHandling.value,
            TicketStatus.handling.value,
            TicketStatus.archived.value,
            TicketStatus.cancelled.value,
            TicketStatus.handled.value,
            TicketStatus.closed.value,
            TicketStatus.spam.value,
          ],
          isOverdue: true,
        );
      case TicketHomeTabs.spam:
        return params.copyWith(
          nodeStatuses: [
            TicketNodeStatus.spam.value,
          ],
          ticketStatuses: [
            TicketStatus.waitingForHandling.value,
            TicketStatus.handling.value,
            TicketStatus.archived.value,
            TicketStatus.cancelled.value,
            TicketStatus.handled.value,
            TicketStatus.closed.value,
            TicketStatus.spam.value,
          ],
        );
      default:
        logDebug("mapTicketHomeTabsToParams missing for ${tab.name}");
        if (kDebugMode) {
          throw Exception("mapTicketHomeTabsToParams missing for ${tab.name}");
        }
        return params;
    }
  }
}

final class HomeCreatedByMyDepartmentEntity extends HomeTabEntity {
  @override
  TicketHomeMenuEnums get homeMenuEnums =>
      TicketHomeMenuEnums.createdByMyDepartment;

  @override
  List<TicketHomeTabs> get tabs => [
        TicketHomeTabs.waitingForHandling,
        TicketHomeTabs.handling,
        TicketHomeTabs.archived,
        TicketHomeTabs.cancelled,
        TicketHomeTabs.handled,
        TicketHomeTabs.closed,
        TicketHomeTabs.outOfDate,
        TicketHomeTabs.spam,
        TicketHomeTabs.all,
      ];

  @override
  String mapTicketHomeTabsToString(TicketHomeTabs tab) {
    switch (tab) {
      case TicketHomeTabs.all:
        return LocaleKeys.ticket_home_menu_tabs_created_by_my_department_all.tr;
      case TicketHomeTabs.waitingForHandling:
        return LocaleKeys
            .ticket_home_menu_tabs_created_by_my_department_waiting_for_handling
            .tr;
      case TicketHomeTabs.handling:
        return LocaleKeys
            .ticket_home_menu_tabs_created_by_my_department_handling.tr;
      case TicketHomeTabs.archived:
        return LocaleKeys
            .ticket_home_menu_tabs_created_by_my_department_archived.tr;
      case TicketHomeTabs.cancelled:
        return LocaleKeys
            .ticket_home_menu_tabs_created_by_my_department_cancelled.tr;
      case TicketHomeTabs.handled:
        return LocaleKeys
            .ticket_home_menu_tabs_created_by_my_department_handled.tr;
      case TicketHomeTabs.outOfDate:
        return LocaleKeys
            .ticket_home_menu_tabs_created_by_my_department_out_of_date.tr;
      case TicketHomeTabs.closed:
        return LocaleKeys
            .ticket_home_menu_tabs_created_by_my_department_closed.tr;
      case TicketHomeTabs.spam:
        return LocaleKeys
            .ticket_home_menu_tabs_created_by_my_department_spam.tr;
      default:
        logDebug("_mapTicketHomeTabsToString error for ${tab.name}");
        return '';
    }
  }

  @override
  TicketListParams mapTicketHomeTabsToParams(TicketHomeTabs tab) {
    final params = defaultParams;
    switch (tab) {
      case TicketHomeTabs.all:
        return params.copyWith(
          ticketStatuses: [
            TicketStatus.waitingForHandling.value,
            TicketStatus.handling.value,
            TicketStatus.archived.value,
            TicketStatus.cancelled.value,
            TicketStatus.handled.value,
            TicketStatus.closed.value,
            TicketStatus.spam.value,
          ],
        );
      case TicketHomeTabs.waitingForHandling:
        return params.copyWith(
          ticketStatuses: [
            TicketStatus.waitingForHandling.value,
          ],
        );

      case TicketHomeTabs.handling:
        return params.copyWith(
          ticketStatuses: [
            TicketStatus.handling.value,
          ],
        );
      case TicketHomeTabs.cancelled:
        return params.copyWith(
          ticketStatuses: [
            TicketStatus.cancelled.value,
          ],
        );
      case TicketHomeTabs.handled:
        return params.copyWith(
          ticketStatuses: [
            TicketStatus.handled.value,
          ],
        );

      case TicketHomeTabs.closed:
        return params.copyWith(
          ticketStatuses: [
            TicketStatus.closed.value,
          ],
        );
      case TicketHomeTabs.archived:
        return params.copyWith(
          ticketStatuses: [
            TicketStatus.archived.value,
          ],
        );
      case TicketHomeTabs.outOfDate:
        return params.copyWith(
          ticketStatuses: [
            TicketStatus.waitingForHandling.value,
            TicketStatus.handling.value,
          ],
          isOverdue: true,
        );
      case TicketHomeTabs.spam:
        return params.copyWith(
          ticketStatuses: [
            TicketStatus.spam.value,
          ],
        );
      default:
        logDebug("mapTicketHomeTabsToParams missing for ${tab.name}");
        if (kDebugMode) {
          throw Exception("mapTicketHomeTabsToParams missing for ${tab.name}");
        }
        return params;
    }
  }
}

final class HomeArchiveSendToMeEntity extends HomeTabEntity {
  @override
  TicketHomeMenuEnums get homeMenuEnums => TicketHomeMenuEnums.archiveSendToMe;

  @override
  List<TicketHomeTabs> get tabs => [
        TicketHomeTabs.waitingForArchive,
        TicketHomeTabs.approved,
        TicketHomeTabs.notApprove,
      ];

  @override
  String mapTicketHomeTabsToString(TicketHomeTabs tab) {
    switch (tab) {
      case TicketHomeTabs.waitingForArchive:
        return LocaleKeys
            .ticket_home_menu_tabs_archive_send_to_me_waiting_for_approve.tr;
      case TicketHomeTabs.approved:
        return LocaleKeys.ticket_home_menu_tabs_archive_send_to_me_approved.tr;
      case TicketHomeTabs.notApprove:
        return LocaleKeys
            .ticket_home_menu_tabs_archive_send_to_me_not_approve.tr;
      default:
        logDebug("_mapTicketHomeTabsToString error for ${tab.name}");
        return '';
    }
  }

  @override
  TicketListParams mapTicketHomeTabsToParams(TicketHomeTabs tab) {
    final params = defaultParams;
    switch (tab) {
      case TicketHomeTabs.waitingForArchive:
        return params.copyWith(
          onHoldStatuses: [
            TicketOnholdRequest.waitingForApprove.value,
          ],
          ticketStatuses: [
            TicketStatus.handling.value,
            TicketStatus.archived.value,
          ],
        );
      case TicketHomeTabs.approved:
        return params.copyWith(
          onHoldStatuses: [
            TicketOnholdRequest.approved.value,
          ],
          // ticketStatuses: [
          //   TicketStatus.waitingForHandling.value,
          //   TicketStatus.archived.value,
          // ],
        );
      case TicketHomeTabs.notApprove:
        return params.copyWith(
          onHoldStatuses: [
            TicketOnholdRequest.notApprove.value,
          ],
          // ticketStatuses: [
          //   TicketStatus.waitingForHandling.value,
          //   TicketStatus.archived.value,
          // ],
        );
      // phục vụ cho case search
      case TicketHomeTabs.all:
        return params.copyWith(
          onHoldStatuses: [
            TicketOnholdRequest.waitingForApprove.value,
            TicketOnholdRequest.approved.value,
            TicketOnholdRequest.notApprove.value,
          ],
          ticketStatuses: [
            TicketStatus.handling.value,
            TicketStatus.archived.value,
          ],
        );
      default:
        logDebug("mapTicketHomeTabsToParams missing for ${tab.name}");
        if (kDebugMode) {
          throw Exception("mapTicketHomeTabsToParams missing for ${tab.name}");
        }
        return params;
    }
  }
}

final class HomeArchiveCreatedByMeEntity extends HomeTabEntity {
  @override
  TicketHomeMenuEnums get homeMenuEnums =>
      TicketHomeMenuEnums.archiveCreatedByMe;

  @override
  List<TicketHomeTabs> get tabs => [
        TicketHomeTabs.waitingForArchive,
        TicketHomeTabs.approved,
        TicketHomeTabs.notApprove,
      ];

  @override
  String mapTicketHomeTabsToString(TicketHomeTabs tab) {
    switch (tab) {
      case TicketHomeTabs.waitingForArchive:
        return LocaleKeys
            .ticket_home_menu_tabs_archive_created_by_me_waiting_for_approve.tr;
      case TicketHomeTabs.approved:
        return LocaleKeys
            .ticket_home_menu_tabs_archive_created_by_me_approved.tr;
      case TicketHomeTabs.notApprove:
        return LocaleKeys
            .ticket_home_menu_tabs_archive_created_by_me_not_approve.tr;
      default:
        logDebug("_mapTicketHomeTabsToString error for ${tab.name}");
        return '';
    }
  }

  @override
  TicketListParams mapTicketHomeTabsToParams(TicketHomeTabs tab) {
    final params = defaultParams;
    switch (tab) {
      case TicketHomeTabs.waitingForArchive:
        return params.copyWith(
          onHoldStatuses: [
            TicketOnholdRequest.waitingForApprove.value,
          ],
          ticketStatuses: [
            TicketStatus.handling.value,
            TicketStatus.archived.value,
          ],
        );
      case TicketHomeTabs.approved:
        return params.copyWith(
          onHoldStatuses: [
            TicketOnholdRequest.approved.value,
          ],
        );

      case TicketHomeTabs.notApprove:
        return params.copyWith(
          onHoldStatuses: [
            TicketOnholdRequest.notApprove.value,
          ],
        );
      // phục vụ cho case search
      case TicketHomeTabs.all:
        return params.copyWith(
          onHoldStatuses: [
            TicketOnholdRequest.waitingForApprove.value,
            TicketOnholdRequest.approved.value,
            TicketOnholdRequest.notApprove.value,
          ],
          ticketStatuses: [
            TicketStatus.handling.value,
            TicketStatus.archived.value,
          ],
        );
      default:
        logDebug("mapTicketHomeTabsToParams missing for ${tab.name}");
        if (kDebugMode) {
          throw Exception("mapTicketHomeTabsToParams missing for ${tab.name}");
        }
        return params;
    }
  }
}

final class HomeNoHandlerEntity extends HomeTabEntity {
  @override
  TicketHomeMenuEnums get homeMenuEnums => TicketHomeMenuEnums.noHandlers;

  @override
  List<TicketHomeTabs> get tabs => [
        TicketHomeTabs.all,
        TicketHomeTabs.outOfDate,
      ];

  @override
  String mapTicketHomeTabsToString(TicketHomeTabs tab) {
    switch (tab) {
      case TicketHomeTabs.all:
        return LocaleKeys.ticket_home_menu_tabs_no_handler_all.tr;
      case TicketHomeTabs.outOfDate:
        return LocaleKeys.ticket_home_menu_tabs_no_handler_out_of_date.tr;
      default:
        logDebug("_mapTicketHomeTabsToString error for ${tab.name}");
        return '';
    }
  }

  @override
  TicketListParams mapTicketHomeTabsToParams(TicketHomeTabs tab) {
    final params = defaultParams;
    switch (tab) {
      case TicketHomeTabs.all:
        return params.copyWith(
          nodeStatuses: [
            TicketNodeStatus.waitingForHandling.value,
          ],
          ticketStatuses: [
            TicketStatus.waitingForHandling.value,
            TicketStatus.handling.value,
            TicketStatus.archived.value,
            TicketStatus.cancelled.value,
            TicketStatus.handled.value,
            TicketStatus.closed.value,
            TicketStatus.spam.value,
          ],
        );
      case TicketHomeTabs.outOfDate:
        return params.copyWith(
          nodeStatuses: [
            TicketNodeStatus.waitingForHandling.value,
            // TicketNodeStatus.handling.value,
          ],
          ticketStatuses: [
            TicketStatus.waitingForHandling.value,
            TicketStatus.handling.value,
            TicketStatus.archived.value,
            TicketStatus.cancelled.value,
            TicketStatus.handled.value,
            TicketStatus.closed.value,
            TicketStatus.spam.value,
          ],
          isOverdue: true,
        );
      default:
        logDebug("mapTicketHomeTabsToParams missing for ${tab.name}");
        if (kDebugMode) {
          throw Exception("mapTicketHomeTabsToParams missing for ${tab.name}");
        }
        return params;
    }
  }
}

final class HomeFlowManagementTabEntity extends HomeTabEntity {
  @override
  TicketHomeMenuEnums get homeMenuEnums => TicketHomeMenuEnums.flowManagement;

  @override
  List<TicketHomeTabs> get tabs => [
        TicketHomeTabs.waitingForHandling,
        TicketHomeTabs.handling,
        TicketHomeTabs.handled,
        TicketHomeTabs.notApprove,
        TicketHomeTabs.approved,
        TicketHomeTabs.waitingForProvideInfo,
        TicketHomeTabs.waitingForArchive,
        TicketHomeTabs.archived,
        TicketHomeTabs.cancelled,
        TicketHomeTabs.outOfDate,
        TicketHomeTabs.spam,
        TicketHomeTabs.all,
      ];

  @override
  String mapTicketHomeTabsToString(TicketHomeTabs tab) {
    switch (tab) {
      case TicketHomeTabs.all:
        return LocaleKeys.ticket_home_menu_tabs_flow_management_all.tr;
      case TicketHomeTabs.waitingForHandling:
        return LocaleKeys
            .ticket_home_menu_tabs_flow_management_waiting_for_handling.tr;
      case TicketHomeTabs.handling:
        return LocaleKeys.ticket_home_menu_tabs_flow_management_handling.tr;
      case TicketHomeTabs.handled:
        return LocaleKeys.ticket_home_menu_tabs_flow_management_handled.tr;
      case TicketHomeTabs.notApprove:
        return LocaleKeys.ticket_home_menu_tabs_flow_management_not_approve.tr;
      case TicketHomeTabs.approved:
        return LocaleKeys.ticket_home_menu_tabs_flow_management_approved.tr;
      case TicketHomeTabs.waitingForProvideInfo:
        return LocaleKeys
            .ticket_home_menu_tabs_flow_management_waiting_for_provide_info.tr;
      case TicketHomeTabs.waitingForArchive:
        return LocaleKeys
            .ticket_home_menu_tabs_flow_management_waiting_for_archive.tr;
      case TicketHomeTabs.archived:
        return LocaleKeys.ticket_home_menu_tabs_flow_management_archived.tr;
      case TicketHomeTabs.cancelled:
        return LocaleKeys.ticket_home_menu_tabs_flow_management_cancelled.tr;
      case TicketHomeTabs.outOfDate:
        return LocaleKeys.ticket_home_menu_tabs_flow_management_out_of_date.tr;
      case TicketHomeTabs.spam:
        return LocaleKeys.ticket_home_menu_tabs_follow_spam.tr;
      default:
        logDebug("_mapTicketHomeTabsToString error for ${tab.name}");
        return '';
    }
  }

  @override
  TicketListParams mapTicketHomeTabsToParams(TicketHomeTabs tab) {
    final params = defaultParams;
    switch (tab) {
      case TicketHomeTabs.all:
        return params.copyWith(
          ticketStatuses: [
            TicketStatus.waitingForHandling.value,
            TicketStatus.handling.value,
            TicketStatus.archived.value,
            TicketStatus.cancelled.value,
            TicketStatus.handled.value,
            TicketStatus.closed.value,
            TicketStatus.spam.value,
          ],
          nodeStatuses: [
            TicketNodeStatus.waitingForHandling.value,
            TicketNodeStatus.handling.value,
            TicketNodeStatus.handled.value,
            TicketNodeStatus.notApprove.value,
            TicketNodeStatus.approved.value,
            TicketNodeStatus.waitingForProvideInfo.value,
            TicketNodeStatus.waitingForArchive.value,
            TicketNodeStatus.archived.value,
            TicketNodeStatus.cancelled.value,
            TicketNodeStatus.spam.value,
          ],
        );
      case TicketHomeTabs.waitingForHandling:
        return params.copyWith(
          ticketStatuses: [
            TicketStatus.waitingForHandling.value,
            TicketStatus.handling.value,
            TicketStatus.archived.value,
            TicketStatus.cancelled.value,
            TicketStatus.handled.value,
            TicketStatus.closed.value,
            TicketStatus.spam.value,
          ],
          nodeStatuses: [
            TicketNodeStatus.waitingForHandling.value,
          ],
        );
      case TicketHomeTabs.handling:
        return params.copyWith(
          ticketStatuses: [
            TicketStatus.waitingForHandling.value,
            TicketStatus.handling.value,
            TicketStatus.archived.value,
            TicketStatus.cancelled.value,
            TicketStatus.handled.value,
            TicketStatus.closed.value,
            TicketStatus.spam.value,
          ],
          nodeStatuses: [
            TicketNodeStatus.handling.value,
          ],
        );
      case TicketHomeTabs.handled:
        return params.copyWith(
          ticketStatuses: [
            TicketStatus.waitingForHandling.value,
            TicketStatus.handling.value,
            TicketStatus.archived.value,
            TicketStatus.cancelled.value,
            TicketStatus.handled.value,
            TicketStatus.closed.value,
            TicketStatus.spam.value,
          ],
          nodeStatuses: [
            TicketNodeStatus.handled.value,
          ],
        );
      case TicketHomeTabs.notApprove:
        return params.copyWith(
          ticketStatuses: [
            TicketStatus.waitingForHandling.value,
            TicketStatus.handling.value,
            TicketStatus.archived.value,
            TicketStatus.cancelled.value,
            TicketStatus.handled.value,
            TicketStatus.closed.value,
            TicketStatus.spam.value,
          ],
          nodeStatuses: [
            TicketNodeStatus.notApprove.value,
          ],
        );
      case TicketHomeTabs.approved:
        return params.copyWith(
          ticketStatuses: [
            TicketStatus.waitingForHandling.value,
            TicketStatus.handling.value,
            TicketStatus.archived.value,
            TicketStatus.cancelled.value,
            TicketStatus.handled.value,
            TicketStatus.closed.value,
            TicketStatus.spam.value,
          ],
          nodeStatuses: [
            TicketNodeStatus.approved.value,
          ],
        );
      case TicketHomeTabs.waitingForProvideInfo:
        return params.copyWith(
          ticketStatuses: [
            TicketStatus.waitingForHandling.value,
            TicketStatus.handling.value,
            TicketStatus.archived.value,
            TicketStatus.cancelled.value,
            TicketStatus.handled.value,
            TicketStatus.closed.value,
            TicketStatus.spam.value,
          ],
          nodeStatuses: [
            TicketNodeStatus.waitingForProvideInfo.value,
          ],
        );
      case TicketHomeTabs.waitingForArchive:
        return params.copyWith(
          ticketStatuses: [
            TicketStatus.waitingForHandling.value,
            TicketStatus.handling.value,
            TicketStatus.archived.value,
            TicketStatus.cancelled.value,
            TicketStatus.handled.value,
            TicketStatus.closed.value,
            TicketStatus.spam.value,
          ],
          nodeStatuses: [
            TicketNodeStatus.waitingForArchive.value,
          ],
        );
      case TicketHomeTabs.archived:
        return params.copyWith(
          ticketStatuses: [
            TicketStatus.waitingForHandling.value,
            TicketStatus.handling.value,
            TicketStatus.archived.value,
            TicketStatus.cancelled.value,
            TicketStatus.handled.value,
            TicketStatus.closed.value,
            TicketStatus.spam.value,
          ],
          nodeStatuses: [
            TicketNodeStatus.archived.value,
          ],
        );
      case TicketHomeTabs.cancelled:
        return params.copyWith(
          ticketStatuses: [
            TicketStatus.waitingForHandling.value,
            TicketStatus.handling.value,
            TicketStatus.archived.value,
            TicketStatus.cancelled.value,
            TicketStatus.handled.value,
            TicketStatus.closed.value,
            TicketStatus.spam.value,
          ],
          nodeStatuses: [
            TicketNodeStatus.cancelled.value,
          ],
        );
      case TicketHomeTabs.outOfDate:
        return params.copyWith(
          ticketStatuses: [
            TicketStatus.waitingForHandling.value,
            TicketStatus.handling.value,
            TicketStatus.archived.value,
            TicketStatus.cancelled.value,
            TicketStatus.handled.value,
            TicketStatus.closed.value,
            TicketStatus.spam.value,
          ],
          nodeStatuses: [
            TicketNodeStatus.waitingForHandling.value,
            // TicketNodeStatus.handling.value,
          ],
          isOverdue: true,
        );
      case TicketHomeTabs.spam:
        return params.copyWith(
          ticketStatuses: [
            TicketStatus.waitingForHandling.value,
            TicketStatus.handling.value,
            TicketStatus.archived.value,
            TicketStatus.cancelled.value,
            TicketStatus.handled.value,
            TicketStatus.closed.value,
            TicketStatus.spam.value,
          ],
          nodeStatuses: [
            TicketNodeStatus.spam.value,
          ],
        );
      default:
        logDebug("mapTicketHomeTabsToParams missing for ${tab.name}");
        if (kDebugMode) {
          throw Exception("mapTicketHomeTabsToParams missing for ${tab.name}");
        }
        return params;
    }
  }
}
